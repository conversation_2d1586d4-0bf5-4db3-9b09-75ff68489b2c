#!/bin/bash
set -e

echo "Setting up Market O'Clock development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 18 (LTS)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm using npm with sudo
sudo npm install -g pnpm

# Navigate to workspace and install backend dependencies
cd /mnt/persist/workspace/backend
pnpm install

# Install frontend dependencies
cd /mnt/persist/workspace/frontend
npm install

# Add Node.js and npm to PATH in profile
echo 'export PATH="/usr/bin:$PATH"' >> $HOME/.profile

echo "Setup completed successfully!"