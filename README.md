# Market O'Clock 🕒

A modern multi-service e-commerce and marketplace platform built with Next.js, NestJS, and Python.

## 🏗️ Project Structure

```
marketoclock/
├── frontend/       # Next.js app
├── backend/        # NestJS API
├── database/       # Shared DB schema, migrations, seeds
├── analytics/      # Python-based or AI engine
├── libs/           # Shared utils (e.g., types, constants, validation)
├── docker/         # Centralized Docker files
├── .env           # Environment variables
├── docker-compose.yml
└── README.md
```

## 🚀 Quick Start

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/marketoclock.git
   cd marketoclock
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. Start the development environment:
   ```bash
   docker-compose up -d
   ```

4. Access the applications:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:4000
   - Database: localhost:5432

## 🛠️ Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Backend**: NestJS with TypeScript
- **Database**: PostgreSQL
- **Analytics**: Python with FastAPI
- **Containerization**: Docker & Docker Compose

## 📦 Services

### Frontend (Next.js)
- Multi-role interface (Admin, Vendor, Customer)
- Server-side rendering
- Type-safe API integration

### Backend (NestJS)
- RESTful API
- Authentication & Authorization
- Product & Order Management
- User Management

### Database
- Shared schema definitions
- Migration scripts
- Seed data
- Type generation

### Analytics
- Data processing
- AI/ML integration
- Business intelligence
- Reporting

## 🔧 Development

### Prerequisites
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 14+

### Development Workflow
1. Create feature branch
2. Make changes
3. Run tests
4. Submit PR

## 📝 License

MIT License - see LICENSE file for details 