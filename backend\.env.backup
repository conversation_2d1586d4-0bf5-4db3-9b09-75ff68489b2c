# Server Configuration
PORT=3001
# Environment Configuration
NODE_ENV=development
# Application Configuration

# Application Name
APP_NAME=MarketOClock
# Application Version
APP_VERSION=1.0.0
# Application Description
APP_DESCRIPTION=MarketOClock is a comprehensive platform for managing market data and analytics.
# Application Author
APP_AUTHOR=AARON RONO
# Application License
APP_LICENSE=MIT
# Application URL
APP_URL=http://localhost:3001
# Application API URL
APP_API_URL=http://localhost:3001/api
# Application Documentation URL
APP_DOCS_URL=http://localhost:3001/docs
# Application Support URL
APP_SUPPORT_URL=http://localhost:3001/support
# Application Terms of Service URL
APP_TERMS_URL=http://localhost:3001/terms
# Application Privacy Policy URL
APP_PRIVACY_URL=http://localhost:3001/privacy

# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=AU110s/6081/2021PG
DB_DATABASE=postgres
DB_USER=postgres
DB_PASS=AU110s/6081/2021PG
DB_NAME=postgres

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/marketoclock

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=62705ab03287da7d41b064ca4a05777003e72affdde145982e5a1f7526b17f29
JWT_EXPIRATION=3600s

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=app.log

# CORS Configuration
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=Content-Type,Authorization

# Rate Limiting Configuration
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX=100

# File Storage Configuration
FILE_STORAGE_PATH=/var/www/marketoclock/uploads

# Third-party API Keys
THIRD_PARTY_API_KEY=your_api_key_here

# Feature Flags
FEATURE_FLAG_NEW_UI=true

# Payment Gateway Configuration
PAYMENT_GATEWAY_API_KEY=your_payment_gateway_api_key

# Analytics Configuration
ANALYTICS_TRACKING_ID=UA-XXXXXXXXX-X

# Debugging Configuration
DEBUG_MODE=false

# Localization Configuration
LOCALE=en-US

# Security Configuration
SECURE_COOKIES=true

# Session Configuration
SESSION_SECRET=your_session_secret

# Backup Configuration
BACKUP_PATH=/var/backups/marketoclock

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_INTERVAL=60s

# Mail Configuration for Zoho Mail
MAIL_HOST=smtp.zoho.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASS=your-zoho-app-specific-password
MAIL_FROM=<EMAIL>
