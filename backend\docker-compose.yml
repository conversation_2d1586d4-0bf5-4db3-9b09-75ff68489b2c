version: '3.8'
<<<<<<< HEAD
services:
  postgres:
    image: postgres:15
    container_name: marketoclock-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: AU110s/6081/2021PG
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
=======

services:
  backend:
    build: .
    ports:
      - "3000:3000"
    env_file:
      - .env.production
    depends_on:
      - db
    restart: always
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  mongodb:
    image: mongo:latest
    container_name: marketoclock-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=marketoclock
    volumes:
      - mongodb_data:/data/db

volumes:
  pgdata:
  mongodb_data:
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
