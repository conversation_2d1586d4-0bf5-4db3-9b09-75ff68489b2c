version: '3.8'

services:
  backend:
    build: .
    ports:
      - "3000:3000"
    env_file:
      - .env.production
    depends_on:
      - db
    restart: always
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  mongodb:
    image: mongo:latest
    container_name: marketoclock-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=marketoclock
    volumes:
      - mongodb_data:/data/db

volumes:
  pgdata:
  mongodb_data: