// Simple script to test JWT functionality
console.log('=== JWT FUNCTIONALITY TEST ===');

try {
  // Import JWT library
  const jwt = require('jsonwebtoken');
  console.log('✅ JWT library loaded successfully');
  
  // Define a secret key
  const secretKey = 'your_jwt_secret_key_here';
  console.log('Secret key defined');
  
  // Create a payload
  const payload = {
    sub: '1234567890',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    iat: Math.floor(Date.now() / 1000)
  };
  console.log('Payload created:', payload);
  
  // Generate a token
  const token = jwt.sign(payload, secretKey, { expiresIn: '1h' });
  console.log('✅ Token generated successfully');
  console.log('Token:', token);
  
  // Verify the token
  const decoded = jwt.verify(token, secretKey);
  console.log('✅ Token verified successfully');
  console.log('Decoded token:', decoded);
  
  // Test with wrong secret
  try {
    const wrongSecret = 'wrong_secret_key';
    jwt.verify(token, wrongSecret);
    console.log('❌ Token verification should have failed with wrong secret');
  } catch (error) {
    console.log('✅ Token verification correctly failed with wrong secret');
  }
  
  // Test with expired token
  try {
    const expiredPayload = { ...payload, exp: Math.floor(Date.now() / 1000) - 3600 };
    const expiredToken = jwt.sign(expiredPayload, secretKey);
    jwt.verify(expiredToken, secretKey);
    console.log('❌ Expired token verification should have failed');
  } catch (error) {
    console.log('✅ Expired token verification correctly failed');
  }
  
  console.log('\n=== JWT TEST SUMMARY ===');
  console.log('JWT functionality is working correctly.');
  console.log('Token generation and verification are successful.');
  console.log('Security checks for wrong secret and expired tokens are working.');
  
} catch (error) {
  console.error('❌ JWT test failed with error:', error.message);
}
