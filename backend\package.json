{"name": "market-o-clock-backend", "version": "0.0.1", "description": "Backend for Market-o-Clock application", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nodemon", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm:generate-migration": "typeorm-ts-node-commonjs -d ./src/data-source.ts migration:generate ./src/migrations", "typeorm:run-migrations": "typeorm-ts-node-commonjs -d ./src/data-source.ts migration:run"}, "dependencies": {"@angular-devkit/schematics": "^19.2.3", "@elastic/elasticsearch": "^8.17.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.3.0", "@nestjs/core": "^11.0.12", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.12", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.3", "@sentry/node": "^9.29.0", "@types/socket.io": "^3.0.2", "aws-sdk": "^2.1692.0", "bcrypt": "5.1.1", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.4.7", "express-rate-limit": "^7.5.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "joi": "^17.12.2", "mongoose": "^8.2.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.14.0", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.21", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/multer": "^1.4.12", "@types/node": "^20.11.30", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "globals": "^16.0.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.2"}}