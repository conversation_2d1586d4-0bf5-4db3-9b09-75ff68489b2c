import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
<<<<<<< HEAD
import { CategoriesModule } from './categories/categories.module';
import { ProductsModule } from './products/products.module';
import { OrdersModule } from './orders/orders.module';
import { NotificationsModule } from './notifications/notifications.module';
import { MicroblogModule } from './microblog/microblog.module';
import { HealthModule } from './health/health.module';
import { CommentsModule } from './comments/comments.module';
import { PostsModule } from './posts/posts.module';
import { PaymentsModule } from './payments/payments.module';
=======
import { AuthMiddleware } from './auth/auth.middleware';
import { CategoriesModule } from './categories/categories.module';
import { ProductsModule } from './products/products.module';
import { MongoDBModule } from './mongodb/mongodb.module';
import { MongoDBSocialModule } from './mongodb/mongodb-social.module';
import mongodbConfig from './config/mongodb.config';
import { MongooseModule } from '@nestjs/mongoose';
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
// import * as Joi from 'joi'; // Uncomment if you decide to use Joi for validation

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [mongodbConfig],
      // Joi validation commented out as per your update; uncomment and install Joi if needed later
      // validationSchema: Joi.object({
      //   DATABASE_HOST: Joi.string().required(),
      //   DATABASE_PORT: Joi.number().default(5432),
      //   DATABASE_USERNAME: Joi.string().required(),
      //   DATABASE_PASSWORD: Joi.string().required(),
      //   DATABASE_NAME: Joi.string().required(),
      //   JWT_SECRET: Joi.string().required(),
      //   JWT_EXPIRATION: Joi.string().default('1d'),
      // }),
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'default_jwt_secret_for_development',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION', '1d'),
        },
      }),
      global: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres' as const,
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 5432),
        username: configService.get<string>('DB_USERNAME', 'postgres'),
        password: configService.get<string>('DB_PASSWORD', 'postgres'),
        database: configService.get<string>('DB_DATABASE', 'postgres'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
<<<<<<< HEAD
        synchronize: configService.get<string>('NODE_ENV') === 'development',
        logging: configService.get<string>('NODE_ENV') === 'development',
        ssl: configService.get<string>('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
=======
        synchronize: true, // Temporarily set to true to update the schema
        logging: process.env.NODE_ENV === 'development', // Optional: Enable logging in dev
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false, // Optional: SSL settings for production
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
      }),
    }),
    UsersModule,
    AuthModule,
    CategoriesModule,
    ProductsModule,
<<<<<<< HEAD
    OrdersModule,
    NotificationsModule,
    MicroblogModule,
    HealthModule,
    CommentsModule,
    PostsModule,
    PaymentsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
  exports: [ConfigModule],
=======
    MongoDBModule,
    MongoDBSocialModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async () => ({
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/marketoclock',
      }),
    }),
  ],
  controllers: [AppController],
  providers: [AppService, AuthMiddleware],
  exports: [ConfigModule] // Export ConfigModule if needed in other modules
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*');
  }
}