import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import * as bcrypt from 'bcrypt';

describe('Authentication System', () => {
  let authService: AuthService;
  let jwtService: JwtService;
  let module: TestingModule;

  const mockJwtService = {
    sign: jest.fn().mockReturnValue('test-token'),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('register', () => {
    it('should register a new user and return a token', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser',
        role: 'user',
        fullName: 'Test User',
        businessName: 'Test Business',
      };

      const hashedPassword = await bcrypt.hash(registerDto.password, 10);
      const mockUser = {
        id: 1,
        ...registerDto,
        password_hash: hashedPassword,
      };

      mockUserRepository.findOne.mockResolvedValue(null);
      mockUserRepository.create.mockReturnValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);

      const result = await authService.register(registerDto);

      expect(result).toBeDefined();
      expect(result.token).toBe('test-token');
      expect(result.user).toBeDefined();
      expect(result.user.email).toBe(registerDto.email);
    });
  });

  describe('login', () => {
    it('should login a user and return a token', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const hashedPassword = await bcrypt.hash(loginDto.password, 10);
      const mockUser = {
        id: 1,
        email: loginDto.email,
        password_hash: hashedPassword,
        username: 'testuser',
        role: 'user',
        fullName: 'Test User',
        businessName: 'Test Business',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await authService.login(loginDto);

      expect(result).toBeDefined();
      expect(result.token).toBe('test-token');
      expect(result.user).toBeDefined();
      expect(result.user.email).toBe(loginDto.email);
    });
  });

  it('should generate JWT token', () => {
    const payload = { sub: 1, email: '<EMAIL>' };
    const token = jwtService.sign(payload);
    expect(token).toBe('test-token');
    expect(mockJwtService.sign).toHaveBeenCalledWith(payload);
  });
});
