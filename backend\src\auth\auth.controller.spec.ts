import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { mockUser } from '../test/test-utils';

describe('AuthController', () => {
  let controller: AuthController;
  let service: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const mockAuthService = {
      register: jest.fn().mockResolvedValue(mockUser),
      login: jest.fn().mockResolvedValue({ token: 'jwt-token', user: mockUser }),
      validateUser: jest.fn().mockResolvedValue(mockUser),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    service = module.get(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should register a new user', async () => {
    const registerDto = {
      email: '<EMAIL>',
      password: 'password',
      first_name: 'Test',
      last_name: 'User',
      role: 'user',
      username: 'testuser'
    };
    expect(await controller.register(registerDto)).toEqual(mockUser);
    expect(service.register).toHaveBeenCalledWith(registerDto);
  });

  it('should login a user', async () => {
    const loginDto = { email: '<EMAIL>', password: 'password' };
    expect(await controller.login(loginDto)).toEqual({ token: 'jwt-token', user: mockUser });
    expect(service.login).toHaveBeenCalledWith(loginDto);
  });
});
