<<<<<<< HEAD
import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';

import { createMockRepository, mockUser } from '../test/test-utils';

// Mock bcrypt properly
jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
  genSalt: jest.fn(),
}));
import * as bcrypt from 'bcryptjs';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let userRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    userRepository = createMockRepository();
    const mockUsersService = {
      findByEmail: jest.fn().mockResolvedValue(mockUser),
      create: jest.fn().mockResolvedValue(mockUser),
    };
    const mockJwtService = {
      sign: jest.fn().mockReturnValue('jwt-token'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: getRepositoryToken(User),
          useValue: userRepository,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should register a new user', async () => {
    const registerDto = {
      email: '<EMAIL>',
      password: 'password',
      username: 'testuser',
      role: 'user',
    };

    (bcrypt.hash as jest.Mock).mockResolvedValue('hashed_password');
    userRepository.create.mockReturnValueOnce(mockUser);
    userRepository.save.mockResolvedValueOnce(mockUser);

    const result = await service.register(registerDto);
    expect(result).toEqual({ token: 'jwt-token', user: mockUser });
    expect(bcrypt.hash).toHaveBeenCalledWith('password', 10);
  });

  it('should validate a user', async () => {
    (bcrypt.compare as jest.Mock).mockResolvedValue(true);
    userRepository.findOne.mockResolvedValueOnce(mockUser);

    const result = await service.validateUser('<EMAIL>', 'password');
    expect(result).toEqual(mockUser);
  });
});
=======
import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import * as bcrypt from 'bcrypt';

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: any;
  let module: TestingModule;

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get(getRepositoryToken(User));
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should login user', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      password_hash: await bcrypt.hash('password', 10),
    };
    mockUserRepository.findOne.mockResolvedValue(mockUser);
    mockJwtService.sign.mockReturnValue('test-token');

    const result = await service.login({ email: '<EMAIL>', password: 'password' });
    expect(result).toBeDefined();
    expect(result.token).toBe('test-token');
    expect(userRepository.findOne).toHaveBeenCalled();
  });
});
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
