import { HttpException, HttpStatus } from '@nestjs/common';

export class BusinessException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus = HttpStatus.BAD_REQUEST,
    public readonly errorCode?: string,
    public readonly details?: any,
  ) {
    super(
      {
        message,
        errorCode,
        details,
        timestamp: new Date().toISOString(),
      },
      statusCode,
    );
  }
}

export class ValidationException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR', details);
  }
}

export class NotFoundBusinessException extends BusinessException {
  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    super(message, HttpStatus.NOT_FOUND, 'RESOURCE_NOT_FOUND', { resource, identifier });
  }
}

export class ConflictException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.CONFLICT, 'CONFLICT_ERROR', details);
  }
}

export class InsufficientPermissionsException extends BusinessException {
  constructor(action: string, resource?: string) {
    const message = resource 
      ? `Insufficient permissions to ${action} ${resource}`
      : `Insufficient permissions to ${action}`;
    super(message, HttpStatus.FORBIDDEN, 'INSUFFICIENT_PERMISSIONS', { action, resource });
  }
}

export class RateLimitException extends BusinessException {
  constructor(limit: number, windowMs: number) {
    super(
      `Rate limit exceeded. Maximum ${limit} requests per ${windowMs}ms`,
      HttpStatus.TOO_MANY_REQUESTS,
      'RATE_LIMIT_EXCEEDED',
      { limit, windowMs },
    );
  }
}

export class ExternalServiceException extends BusinessException {
  constructor(service: string, operation: string, originalError?: any) {
    super(
      `External service error: ${service} - ${operation}`,
      HttpStatus.BAD_GATEWAY,
      'EXTERNAL_SERVICE_ERROR',
      { service, operation, originalError: originalError?.message },
    );
  }
}
