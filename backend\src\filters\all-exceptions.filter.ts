import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number;
    let message: string;
    let error: string;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      message = typeof exceptionResponse === 'string' 
        ? exceptionResponse 
        : (exceptionResponse as any).message || exception.message;
      error = exception.name;
    } else if (exception instanceof Error) {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : exception.message;
      error = exception.name;
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Internal server error';
      error = 'UnknownError';
    }

    const correlationId = this.generateCorrelationId();
    const timestamp = new Date().toISOString();

    const errorResponse = {
      statusCode: status,
      timestamp,
      path: request.url,
      method: request.method,
      message,
      error: process.env.NODE_ENV === 'production' && status >= 500 ? undefined : error,
      correlationId,
    };

    // Log the error
    this.logError(exception, request, correlationId, status);

    response.status(status).json(errorResponse);
  }

  private logError(
    exception: unknown,
    request: Request,
    correlationId: string,
    status: number,
  ): void {
    const { method, url, body, query, params, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const ip = headers['x-forwarded-for'] || request.connection.remoteAddress;

    const logContext = {
      correlationId,
      method,
      url,
      statusCode: status,
      userAgent,
      ip,
      body: this.sanitizeBody(body),
      query,
      params,
    };

    if (exception instanceof Error) {
      logContext['stack'] = exception.stack;
      logContext['name'] = exception.name;
    }

    if (status >= 500) {
      this.logger.error(
        `${method} ${url} - ${status} - ${exception instanceof Error ? exception.message : 'Unknown error'}`,
        JSON.stringify(logContext, null, 2),
      );
    } else if (status >= 400) {
      this.logger.warn(
        `${method} ${url} - ${status} - ${exception instanceof Error ? exception.message : 'Unknown error'}`,
        JSON.stringify(logContext, null, 2),
      );
    }
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
