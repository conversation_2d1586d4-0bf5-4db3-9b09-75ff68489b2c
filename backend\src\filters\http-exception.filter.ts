// src/filters/http-exception.filter.ts
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { Request, Response } from 'express';

interface ErrorResponse {
  statusCode: number;
  timestamp: string;
  path: string;
  method: string;
  message: string | object;
  error?: string;
  details?: any;
  correlationId?: string;
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    // Generate correlation ID for tracking
    const correlationId = this.generateCorrelationId();

    // Extract error details
    const errorResponse: ErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: this.extractMessage(exceptionResponse),
      correlationId,
    };

    // Add error name for client debugging
    if (status >= 500) {
      errorResponse.error = exception.name;
    }

    // Add validation details if available
    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const responseObj = exceptionResponse as any;
      if (responseObj.message && Array.isArray(responseObj.message)) {
        errorResponse.details = responseObj.message;
      }
    }

    // Log error details
    this.logError(exception, request, correlationId, status);

    // Remove sensitive information in production
    if (process.env.NODE_ENV === 'production' && status >= 500) {
      errorResponse.message = 'Internal server error';
      delete errorResponse.details;
    }

    response.status(status).json(errorResponse);
  }

  private extractMessage(exceptionResponse: string | object): string | object {
    if (typeof exceptionResponse === 'string') {
      return exceptionResponse;
    }

    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const responseObj = exceptionResponse as any;
      return responseObj.message || responseObj.error || 'An error occurred';
    }

    return 'An error occurred';
  }

  private logError(
    exception: HttpException,
    request: Request,
    correlationId: string,
    status: number
  ): void {
    const { method, url, body, query, params, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const ip = headers['x-forwarded-for'] || request.connection.remoteAddress;

    const logContext = {
      correlationId,
      method,
      url,
      statusCode: status,
      userAgent,
      ip,
      body: this.sanitizeBody(body),
      query,
      params,
      stack: exception.stack,
    };

    if (status >= 500) {
      this.logger.error(
        `${method} ${url} - ${status} - ${exception.message}`,
        JSON.stringify(logContext, null, 2)
      );
    } else if (status >= 400) {
      this.logger.warn(
        `${method} ${url} - ${status} - ${exception.message}`,
        JSON.stringify(logContext, null, 2)
      );
    }
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}


