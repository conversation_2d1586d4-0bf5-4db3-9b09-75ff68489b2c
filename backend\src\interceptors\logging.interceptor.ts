import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { method, url, body, query, params } = request;
    const userAgent = request.headers['user-agent'] || '';
    const ip = request.headers['x-forwarded-for'] || request.connection.remoteAddress;

    const startTime = Date.now();
    const correlationId = this.generateCorrelationId();

    // Add correlation ID to response headers
    response.setHeader('X-Correlation-ID', correlationId);

    this.logger.log(
      `Incoming Request: ${method} ${url}`,
      JSON.stringify({
        correlationId,
        method,
        url,
        userAgent,
        ip,
        body: this.sanitizeBody(body),
        query,
        params,
        timestamp: new Date().toISOString(),
      }, null, 2),
    );

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        const statusCode = response.statusCode;

        this.logger.log(
          `Outgoing Response: ${method} ${url} - ${statusCode} - ${duration}ms`,
          JSON.stringify({
            correlationId,
            method,
            url,
            statusCode,
            duration,
            responseSize: JSON.stringify(data).length,
            timestamp: new Date().toISOString(),
          }, null, 2),
        );
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        this.logger.error(
          `Error Response: ${method} ${url} - ${statusCode} - ${duration}ms`,
          JSON.stringify({
            correlationId,
            method,
            url,
            statusCode,
            duration,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
          }, null, 2),
        );

        throw error;
      }),
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
