import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Delete,
  Put,
} from '@nestjs/common';
import { SocialService } from '../services/social.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import {
  CreatePostDto,
  CreateCommentDto,
  SharePostDto,
  AddReactionDto,
  PaginationDto,
  UserMentionDto,
  FollowUserDto,
  NotificationPreferencesDto,
} from '../dto/social.dto';
import { Request as ExpressRequest } from 'express';

interface RequestWithUser extends ExpressRequest {
  user: {
    id: string;
    email: string;
    role: string;
  };
}

@ApiTags('Social')
@Controller('social')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SocialController {
  constructor(private readonly socialService: SocialService) {}

  @Post('posts')
  @ApiOperation({ summary: 'Create a new post' })
  @ApiResponse({ status: 201, description: 'Post created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createPost(@Request() req: RequestWithUser, @Body() createPostDto: CreatePostDto) {
    return this.socialService.createPost(req.user.id, createPostDto.content, createPostDto.imageUrl);
  }

  @Get('posts/:id')
  @ApiOperation({ summary: 'Get a post by ID' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 200, description: 'Post retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async getPost(@Param('id') id: string) {
    return this.socialService.getPost(id);
  }

  @Post('posts/:id/comments')
  @ApiOperation({ summary: 'Add a comment to a post' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 201, description: 'Comment added successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async addComment(
    @Request() req: RequestWithUser,
    @Param('id') postId: string,
    @Body() createCommentDto: CreateCommentDto
  ) {
    return this.socialService.addComment(req.user.id, postId, createCommentDto.content);
  }

  @Post('posts/:id/like')
  @ApiOperation({ summary: 'Like a post' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 200, description: 'Post liked successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async likePost(@Request() req: RequestWithUser, @Param('id') postId: string) {
    return this.socialService.likePost(req.user.id, postId);
  }

  @Delete('posts/:id/like')
  @ApiOperation({ summary: 'Unlike a post' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 200, description: 'Post unliked successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async unlikePost(@Request() req: RequestWithUser, @Param('id') postId: string) {
    return this.socialService.unlikePost(req.user.id, postId);
  }

  @Post('posts/:id/share')
  @ApiOperation({ summary: 'Share a post' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 201, description: 'Post shared successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async sharePost(
    @Request() req: RequestWithUser,
    @Param('id') postId: string,
    @Body() sharePostDto: SharePostDto
  ) {
    return this.socialService.sharePost(req.user.id, postId, sharePostDto.shareContent);
  }

  @Post('posts/:id/reactions')
  @ApiOperation({ summary: 'Add a reaction to a post' })
  @ApiParam({ name: 'id', description: 'Post ID' })
  @ApiResponse({ status: 200, description: 'Reaction added successfully' })
  @ApiResponse({ status: 400, description: 'Invalid reaction type' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async addReaction(
    @Request() req: RequestWithUser,
    @Param('id') postId: string,
    @Body() addReactionDto: AddReactionDto
  ) {
    return this.socialService.addReaction(req.user.id, postId, addReactionDto.reactionType);
  }

  @Get('feed')
  @ApiOperation({ summary: 'Get user feed' })
  @ApiQuery({ type: PaginationDto })
  @ApiResponse({ status: 200, description: 'Feed retrieved successfully' })
  async getUserFeed(
    @Request() req: RequestWithUser,
    @Query() paginationDto: PaginationDto
  ) {
    return this.socialService.getUserFeed(
      req.user.id,
      paginationDto.page,
      paginationDto.limit
    );
  }

  @Post('mentions')
  @ApiOperation({ summary: 'Mention users in a post' })
  @ApiResponse({ status: 201, description: 'Users mentioned successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  async mentionUsers(
    @Request() req: RequestWithUser,
    @Body() userMentionDto: UserMentionDto
  ) {
    return this.socialService.mentionUsers(req.user.id, userMentionDto);
  }

  @Post('follow')
  @ApiOperation({ summary: 'Follow a user' })
  @ApiResponse({ status: 201, description: 'User followed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async followUser(
    @Request() req: RequestWithUser,
    @Body() followUserDto: FollowUserDto
  ) {
    return this.socialService.followUser(req.user.id, followUserDto.userId);
  }

  @Delete('follow/:userId')
  @ApiOperation({ summary: 'Unfollow a user' })
  @ApiParam({ name: 'userId', description: 'User ID to unfollow' })
  @ApiResponse({ status: 200, description: 'User unfollowed successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async unfollowUser(
    @Request() req: RequestWithUser,
    @Param('userId') userId: string
  ) {
    return this.socialService.unfollowUser(req.user.id, userId);
  }

  @Get('notifications')
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiQuery({ type: PaginationDto })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  async getNotifications(
    @Request() req: RequestWithUser,
    @Query() paginationDto: PaginationDto
  ) {
    return this.socialService.getNotifications(
      req.user.id,
      paginationDto.page,
      paginationDto.limit
    );
  }

  @Put('notifications/preferences')
  @ApiOperation({ summary: 'Update notification preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })  async updateNotificationPreferences(
    @Body() preferences: NotificationPreferencesDto
  ) {
    return this.socialService.updateNotificationPreferences(preferences);
  }

  @Put('notifications/:id/read')
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markNotificationAsRead(@Param('id') id: string) {
    return this.socialService.markNotificationAsRead(id);
  }

  @Put('notifications/read-all')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllNotificationsAsRead(@Request() req: RequestWithUser) {
    return this.socialService.markAllNotificationsAsRead(req.user.id);
  }
}