import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray, IsNumber, Min, Max, IsBoolean, IsUrl } from 'class-validator';
import { ReactionType } from '../schemas/user-interaction.schema';

export class CreatePostDto {
  @ApiProperty({ description: 'Content of the post', example: 'Hello world!' })
  @IsString()
  content!: string;

  @ApiProperty({ description: 'URL of the post image', required: false, example: 'https://example.com/image.jpg' })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;
}

export class CreateCommentDto {
  @ApiProperty({ description: 'Content of the comment', example: 'Great post!' })
  @IsString()
  content!: string;
}

export class SharePostDto {
  @ApiProperty({ description: 'Additional content for the share', required: false, example: 'Check this out!' })
  @IsOptional()
  @IsString()
  shareContent?: string;
}

export class AddReactionDto {
  @ApiProperty({ 
    description: 'Type of reaction',
    enum: ReactionType,
    example: ReactionType.LIKE
  })
  @IsEnum(ReactionType)
  reactionType!: ReactionType;
}

export class PaginationDto {
  @ApiProperty({ description: 'Page number', default: 1, minimum: 1 })
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({ description: 'Number of items per page', default: 20, minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1)
  @Max(100)
  limit: number = 20;
}

export class UserMentionDto {
  @ApiProperty({ 
    description: 'Array of user IDs to mention',
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012']
  })
  @IsArray()
  @IsString({ each: true })
  userIds!: string[];

  @ApiProperty({ description: 'Message containing the mentions', example: 'Hey @user1 and @user2!' })
  @IsString()
  message!: string;
}

export class FollowUserDto {
  @ApiProperty({ description: 'ID of the user to follow', example: '507f1f77bcf86cd799439011' })
  @IsString()
  userId!: string;
}

export class NotificationPreferencesDto {
  @ApiProperty({ description: 'Receive notifications for new posts', default: true })
  @IsOptional()
  @IsBoolean()
  post?: boolean;

  @ApiProperty({ description: 'Receive notifications for new comments', default: true })
  @IsOptional()
  @IsBoolean()
  comment?: boolean;

  @ApiProperty({ description: 'Receive notifications for new likes', default: true })
  @IsOptional()
  @IsBoolean()
  like?: boolean;

  @ApiProperty({ description: 'Receive notifications for new follows', default: true })
  @IsOptional()
  @IsBoolean()
  follow?: boolean;

  @ApiProperty({ description: 'Receive notifications for mentions', default: true })
  @IsOptional()
  @IsBoolean()
  mention?: boolean;
} 