import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseGuards } from '@nestjs/common';
import { WsJwtAuthGuard } from '../../auth/guards/ws-jwt-auth.guard';
import { SocialPost } from '../schemas/social-post.schema';
import { UserInteraction } from '../schemas/user-interaction.schema';
import { Follower } from '../schemas/follower.schema';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: 'social',
})
export class SocialGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private userSockets: Map<string, Socket[]> = new Map();

  constructor() {}

  async handleConnection(client: Socket) {
    try {
      const userId = await this.validateConnection(client);
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, []);
      }
      this.userSockets.get(userId)?.push(client);
    } catch (error) {
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data.user?.id;
    if (userId) {
      const sockets = this.userSockets.get(userId);
      if (sockets) {
        const index = sockets.indexOf(client);
        if (index > -1) {
          sockets.splice(index, 1);
        }
        if (sockets.length === 0) {
          this.userSockets.delete(userId);
        }
      }
    }
  }

  @UseGuards(WsJwtAuthGuard)
  @SubscribeMessage('joinPost')
  async handleJoinPost(
    @ConnectedSocket() client: Socket,
    @MessageBody() postId: string,
  ) {
    client.join(`post:${postId}`);
  }

  @UseGuards(WsJwtAuthGuard)
  @SubscribeMessage('leavePost')
  async handleLeavePost(
    @ConnectedSocket() client: Socket,
    @MessageBody() postId: string,
  ) {
    client.leave(`post:${postId}`);
  }

  private async validateConnection(client: Socket): Promise<string> {
    const token = client.handshake.headers.authorization?.split(' ')[1];
    if (!token) {
      throw new Error('No token provided');
    }
    // Validate token and return userId
    return client.data.user?.id;
  }

  async emitNewPost(userId: string, post: SocialPost) {
    const sockets = this.userSockets.get(userId);
    if (sockets) {
      sockets.forEach(socket => {
        socket.emit('newPost', post);
      });
    }
  }

  async emitNewComment(postId: string, comment: UserInteraction) {
    this.server.to(`post:${postId}`).emit('newComment', comment);
  }

  async emitNewLike(postId: string, like: UserInteraction) {
    this.server.to(`post:${postId}`).emit('newLike', like);
  }

  async emitUnlike(postId: string, userId: string) {
    this.server.to(`post:${postId}`).emit('unlike', { userId });
  }

  async emitNewFollow(followingId: string, follow: Follower) {
    const sockets = this.userSockets.get(followingId);
    if (sockets) {
      sockets.forEach(socket => {
        socket.emit('newFollow', follow);
      });
    }
  }

  async emitUnfollow(followingId: string, followerId: string) {
    const sockets = this.userSockets.get(followingId);
    if (sockets) {
      sockets.forEach(socket => {
        socket.emit('unfollow', { followerId });
      });
    }
  }

  async emitNewShare(postId: string, share: UserInteraction) {
    this.server.to(`post:${postId}`).emit('newShare', share);
  }

  async emitReactionUpdate(postId: string, reaction: UserInteraction) {
    this.server.to(`post:${postId}`).emit('reactionUpdate', reaction);
  }

  async emitNewReaction(postId: string, reaction: UserInteraction) {
    this.server.to(`post:${postId}`).emit('newReaction', reaction);
  }

  async emitNewMention(userId: string, mention: UserInteraction) {
    const sockets = this.userSockets.get(userId);
    if (sockets) {
      sockets.forEach(socket => {
        socket.emit('newMention', mention);
      });
    }
  }
} 