import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SocialPost, SocialPostSchema } from './schemas/social-post.schema';
import { UserInteraction, UserInteractionSchema } from './schemas/user-interaction.schema';
import { Notification, NotificationSchema } from './schemas/notification.schema';
import { Follower, FollowerSchema } from './schemas/follower.schema';
import { UserFeed, UserFeedSchema } from './schemas/user-feed.schema';
import { SocialService } from './services/social.service';
import { SocialController } from './controllers/social.controller';
import { SocialGateway } from './gateways/social.gateway';
import { QueueSyncService } from './services/queue-sync.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: SocialPost.name, schema: SocialPostSchema },
      { name: UserFeed.name, schema: UserFeedSchema },
      { name: UserInteraction.name, schema: UserInteractionSchema },
      { name: Notification.name, schema: NotificationSchema },
      { name: Follower.name, schema: FollowerSchema },
    ]),
    AuthModule,
  ],
  controllers: [SocialController],
  providers: [SocialService, SocialGateway, QueueSyncService],
  exports: [SocialService, QueueSyncService],
})
export class MongoDBSocialModule {}