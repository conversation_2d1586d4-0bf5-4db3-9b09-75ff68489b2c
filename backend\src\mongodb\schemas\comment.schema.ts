import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Comment extends Document {
  @Prop({ required: true })
  content!: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  authorId!: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId, ref: 'SocialPost' })
  postId!: Types.ObjectId;

  @Prop({ default: 0 })
  likesCount: number = 0;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], default: [] })
  likedBy: Types.ObjectId[] = [];

  @Prop({ default: Date.now })
  createdAt: Date = new Date();

  @Prop({ default: Date.now })
  updatedAt: Date = new Date();
}

export const CommentSchema = SchemaFactory.createForClass(Comment); 