import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Follower extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  followerId!: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  followingId!: Types.ObjectId;

  @Prop({ type: Date, default: Date.now })
  createdAt!: Date;
}

export const FollowerSchema = SchemaFactory.createForClass(Follower);

// Create compound index for unique follower-following relationship
FollowerSchema.index({ followerId: 1, followingId: 1 }, { unique: true });

// Create indexes for better query performance
FollowerSchema.index({ followerId: 1 });
FollowerSchema.index({ followingId: 1 });
FollowerSchema.index({ createdAt: -1 }); 