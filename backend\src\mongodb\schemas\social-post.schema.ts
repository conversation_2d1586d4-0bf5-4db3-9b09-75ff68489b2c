import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class SocialPost extends Document {
  @Prop({ type: String, required: true })
  content!: string;

  @Prop({ type: String })
  imageUrl?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId!: Types.ObjectId;

  @Prop({ type: Number, default: 0 })
  likes!: number;

  @Prop({ type: Number, default: 0 })
  comments!: number;

  @Prop({ type: Number, default: 0 })
  shares!: number;

  @Prop({ type: [Types.ObjectId], ref: 'User', default: [] })
  likedBy!: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Comment', default: [] })
  commentIds!: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'SocialPost' })
  sharedFrom?: Types.ObjectId;

  @Prop({ type: Date, default: Date.now })
  createdAt!: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt!: Date;
}

export const SocialPostSchema = SchemaFactory.createForClass(SocialPost);

// Create indexes for better query performance
SocialPostSchema.index({ userId: 1 });
SocialPostSchema.index({ createdAt: -1 });
SocialPostSchema.index({ sharedFrom: 1 }); 