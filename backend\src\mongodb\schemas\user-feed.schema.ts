import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserFeed extends Document {
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId!: Types.ObjectId;

  @Prop({
    type: [{
      postId: { type: Types.ObjectId, ref: 'SocialPost' },
      createdAt: { type: Date, default: Date.now },
      type: { type: String, enum: ['post', 'share', 'mention'] },
    }],
    default: [],
  })
  feedItems: Array<{
    postId: Types.ObjectId;
    createdAt: Date;
    type: 'post' | 'share' | 'mention';
  }> = [];

  @Prop({ default: Date.now })
  lastUpdated: Date = new Date();
}

export const UserFeedSchema = SchemaFactory.createForClass(UserFeed); 