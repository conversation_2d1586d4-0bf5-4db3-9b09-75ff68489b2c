import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum InteractionType {
  MENTION = 'MENTION',
  TAG = 'TAG',
  REACTION = 'REACTION',
  BOOKMARK = 'BOOKMARK',
}

export enum ReactionType {
  LIKE = 'LIKE',
  LOVE = 'LOVE',
  HAHA = 'HAHA',
  WOW = 'WOW',
  SAD = 'SAD',
  ANGRY = 'ANGRY',
}

@Schema({ timestamps: true })
export class UserInteraction extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId!: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  targetUserId?: Types.ObjectId;

  @Prop({ type: String, enum: InteractionType, required: true })
  type!: InteractionType;

  @Prop({ type: Types.ObjectId, ref: 'SocialPost' })
  postId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Comment' })
  commentId?: Types.ObjectId;

  @Prop({ type: String, enum: ReactionType })
  reactionType?: ReactionType;

  @Prop({ type: String })
  content?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt!: Date;
}

export const UserInteractionSchema = SchemaFactory.createForClass(UserInteraction);

// Create indexes for better query performance
UserInteractionSchema.index({ userId: 1 });
UserInteractionSchema.index({ targetUserId: 1 });
UserInteractionSchema.index({ postId: 1 });
UserInteractionSchema.index({ commentId: 1 });
UserInteractionSchema.index({ type: 1 });
UserInteractionSchema.index({ createdAt: -1 }); 