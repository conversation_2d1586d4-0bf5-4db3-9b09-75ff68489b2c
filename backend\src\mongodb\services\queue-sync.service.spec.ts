import { Test, TestingModule } from '@nestjs/testing';
import { QueueSyncService } from './queue-sync.service';
import { getModelToken } from '@nestjs/mongoose';
import { SocialPost } from '../schemas/social-post.schema';
import { SocialGateway } from '../gateways/social.gateway';

describe('QueueSyncService', () => {
  let service: QueueSyncService;
  let module: TestingModule;

  const mockSocialPostModel = {
    findById: jest.fn(),
    create: jest.fn(),
    findOneAndUpdate: jest.fn(),
  };

  const mockSocialGateway = {
    emitNewPost: jest.fn(),
    emitPostUpdate: jest.fn(),
    emitPostDeletion: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        QueueSyncService,
        {
          provide: getModelToken(SocialPost.name),
          useValue: mockSocialPostModel,
        },
        {
          provide: SocialGateway,
          useValue: mockSocialGateway,
        },
      ],
    }).compile();

    service = module.get<QueueSyncService>(QueueSyncService);
  });

  afterEach(async () => {
    service.stop();
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should add item to queue', async () => {
    const item = {
      type: 'post' as const,
      action: 'create' as const,
      data: { content: 'test' },
      timestamp: new Date(),
    };

    await service.addToQueue(item);
    // Add assertions based on your implementation
  });

  it('should process queue items', async () => {
    const item = {
      type: 'post' as const,
      action: 'create' as const,
      data: { content: 'test' },
      timestamp: new Date(),
    };

    await service.addToQueue(item);
    // Add assertions based on your implementation
  });
}); 