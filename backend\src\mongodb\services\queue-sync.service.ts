import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SocialPost } from '../schemas/social-post.schema';
import { SocialGateway } from '../gateways/social.gateway';

interface SyncQueueItem {
  type: 'post' | 'comment' | 'like' | 'follow' | 'mention';
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: Date;
}

@Injectable()
export class QueueSyncService implements OnModuleInit, OnModuleDestroy {
  private syncQueue: SyncQueueItem[] = [];
  private isProcessing = false;
  private readonly BATCH_SIZE = 100;
  private readonly PROCESS_INTERVAL = 5000; // 5 seconds
  private intervalId: NodeJS.Timeout | undefined;

  constructor(
    @InjectModel(SocialPost.name) private socialPostModel: Model<SocialPost>,
    private socialGateway: SocialGateway,
  ) {}

  onModuleInit() {
    this.startQueueProcessing();
  }

  onModuleDestroy() {
    this.stop();
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  private startQueueProcessing() {
    this.intervalId = setInterval(async () => {
      if (!this.isProcessing && this.syncQueue.length > 0) {
        await this.processQueue();
      }
    }, this.PROCESS_INTERVAL);
  }

  async addToQueue(item: SyncQueueItem) {
    this.syncQueue.push(item);
  }

  private async processQueue() {
    this.isProcessing = true;
    try {
      const batch = this.syncQueue.splice(0, this.BATCH_SIZE);
      for (const item of batch) {
        await this.processItem(item);
      }
    } catch (error) {
      console.error('Error processing sync queue:', error);
      // Requeue failed items
      this.syncQueue.unshift(...this.syncQueue.splice(0, this.BATCH_SIZE));
    } finally {
      this.isProcessing = false;
    }
  }

  private async processItem(item: SyncQueueItem) {
    switch (item.type) {
      case 'post':
        await this.syncPost(item);
        break;
      case 'comment':
        await this.syncComment(item);
        break;
      case 'like':
        await this.syncLike(item);
        break;
      case 'follow':
        await this.syncFollow(item);
        break;
      case 'mention':
        await this.syncMention(item);
        break;
    }
  }

  private async syncPost(item: SyncQueueItem) {
    const { action, data } = item;
    switch (action) {
      case 'create':
        await this.socialPostModel.create(data);
        await this.notifyFollowers(data.authorId, data);
        break;
      case 'update':
        await this.socialPostModel.findByIdAndUpdate(data._id, data);
        break;
      case 'delete':
        await this.socialPostModel.findByIdAndDelete(data._id);
        break;
    }
  }

  private async syncComment(item: SyncQueueItem) {
    // TODO: Implement comment synchronization
    console.log('Syncing comment:', item);
  }

  private async syncLike(item: SyncQueueItem) {
    // TODO: Implement like synchronization
    console.log('Syncing like:', item);
  }

  private async syncFollow(item: SyncQueueItem) {
    // TODO: Implement follow synchronization
    console.log('Syncing follow:', item);
  }

  private async syncMention(item: SyncQueueItem) {
    // TODO: Implement mention synchronization
    console.log('Syncing mention:', item);
  }

  private async notifyFollowers(userId: string, data: any) {
    const followers = await this.getFollowers(userId);
    for (const follower of followers) {
      await this.socialGateway.emitNewPost(follower, data);
    }
  }

  private async getFollowers(userId: string): Promise<string[]> {
    // TODO: Implement follower retrieval from database
    console.log('Getting followers for user:', userId);
    return [];
  }

  // Methods to be called from other services
  async syncPostCreation(post: any) {
    await this.addToQueue({
      type: 'post',
      action: 'create',
      data: post,
      timestamp: new Date(),
    });
  }

  async syncPostUpdate(post: any) {
    await this.addToQueue({
      type: 'post',
      action: 'update',
      data: post,
      timestamp: new Date(),
    });
  }

  async syncPostDeletion(postId: string) {
    await this.addToQueue({
      type: 'post',
      action: 'delete',
      data: { _id: postId },
      timestamp: new Date(),
    });
  }
} 