import { Test, TestingModule } from '@nestjs/testing';
import { SocialService } from './social.service';
import { getModelToken } from '@nestjs/mongoose';
import { SocialPost } from '../schemas/social-post.schema';
import { UserFeed } from '../schemas/user-feed.schema';
import { Follower } from '../schemas/follower.schema';
import { Notification } from '../schemas/notification.schema';
import { UserInteraction } from '../schemas/user-interaction.schema';
import { SocialGateway } from '../gateways/social.gateway';
import { Types } from 'mongoose';

describe('SocialService', () => {
  let service: SocialService;

  const mockSocialPostModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockUserFeedModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockFollowerModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockNotificationModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockUserInteractionModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockSocialGateway = {
    handleNewPost: jest.fn(),
    handleNewComment: jest.fn(),
    handleNewLike: jest.fn(),
    handleNewFollow: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialService,
        {
          provide: getModelToken(SocialPost.name),
          useValue: mockSocialPostModel,
        },
        {
          provide: getModelToken(UserFeed.name),
          useValue: mockUserFeedModel,
        },
        {
          provide: getModelToken(Follower.name),
          useValue: mockFollowerModel,
        },
        {
          provide: getModelToken(Notification.name),
          useValue: mockNotificationModel,
        },
        {
          provide: getModelToken(UserInteraction.name),
          useValue: mockUserInteractionModel,
        },
        {
          provide: SocialGateway,
          useValue: mockSocialGateway,
        },
      ],
    }).compile();

    service = module.get<SocialService>(SocialService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a new post', async () => {
    const mockPost = {
      userId: new Types.ObjectId('507f1f77bcf86cd799439011'),
      content: 'Test post',
      mediaUrls: [],
    };
    mockSocialPostModel.create.mockResolvedValue(mockPost);

    const result = await service.createPost('507f1f77bcf86cd799439011', 'Test post');
    expect(result).toBeDefined();
    expect(mockSocialPostModel.create).toHaveBeenCalled();
  });
}); 