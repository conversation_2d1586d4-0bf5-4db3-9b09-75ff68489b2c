import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SocialPost } from '../schemas/social-post.schema';
import { UserFeed } from '../schemas/user-feed.schema';
import { Follower } from '../schemas/follower.schema';
import { Notification } from '../schemas/notification.schema';
import { UserInteraction } from '../schemas/user-interaction.schema';
import { SocialGateway } from '../gateways/social.gateway';
import { ReactionType } from '../schemas/user-interaction.schema';
import { NotificationPreferencesDto } from '../dto/social.dto';

@Injectable()
export class SocialService {
  constructor(
    @InjectModel(SocialPost.name) private socialPostModel: Model<SocialPost>,
    @InjectModel(UserFeed.name) private userFeedModel: Model<UserFeed>,
    @InjectModel(Follower.name) private followerModel: Model<Follower>,
    @InjectModel(Notification.name) private notificationModel: Model<Notification>,
    @InjectModel(UserInteraction.name) private userInteractionModel: Model<UserInteraction>,
    private readonly socialGateway: SocialGateway,
  ) {}

  // Post methods
  async createPost(userId: string, content: string, imageUrl?: string): Promise<SocialPost> {
    const post = await this.socialPostModel.create({
      userId: new Types.ObjectId(userId),
      content,
      imageUrl,
      likes: 0,
      comments: 0,
      shares: 0,
    });

    if (post._id) {
      // Update followers' feeds with the new post
      await this.updateFollowersFeeds(userId, post._id as unknown as Types.ObjectId);
      await this.notifyFollowers(userId, 'post', post._id.toString());
      this.socialGateway.emitNewPost(userId, post);
    }

    return post;
  }

  async getPost(id: string): Promise<SocialPost> {
    const post = await this.socialPostModel.findById(id);
    if (!post) {
      throw new NotFoundException('Post not found');
    }
    return post;
  }

  // Comment methods
  async addComment(userId: string, postId: string, content: string): Promise<UserInteraction> {
    const post = await this.socialPostModel.findById(postId);
    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const comment = await this.userInteractionModel.create({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'COMMENT',
      content,
    });

    post.comments += 1;
    await post.save();

    if (comment._id) {
      await this.createNotification(
        post.userId.toString(),
        userId,
        'comment',
        postId,
        comment._id.toString()
      );
      this.socialGateway.emitNewComment(postId, comment);
    }

    return comment;
  }

  // Like methods
  async likePost(userId: string, postId: string): Promise<void> {
    const post = await this.socialPostModel.findById(postId);
    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const existingLike = await this.userInteractionModel.findOne({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'REACTION',
      reactionType: ReactionType.LIKE,
    });

    if (existingLike) {
      throw new BadRequestException('Post already liked');
    }

    const like = await this.userInteractionModel.create({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'REACTION',
      reactionType: ReactionType.LIKE,
    });

    post.likes += 1;
    await post.save();

    await this.createNotification(
      post.userId.toString(),
      userId,
      'like',
      postId
    );
    this.socialGateway.emitNewLike(postId, like);
  }

  async unlikePost(userId: string, postId: string): Promise<void> {
    const post = await this.socialPostModel.findById(postId);
    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const like = await this.userInteractionModel.findOneAndDelete({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'REACTION',
      reactionType: ReactionType.LIKE,
    });

    if (like) {
      post.likes -= 1;
      await post.save();
      this.socialGateway.emitUnlike(postId, userId);
    }
  }

  // Feed methods
  async getUserFeed(userId: string, page: number = 1, limit: number = 20): Promise<any> {
    const following = await this.followerModel.find({ followerId: new Types.ObjectId(userId) }).select('followingId');
    const followingIds = following.map(f => f.followingId);

    const posts = await this.socialPostModel
      .find({ userId: { $in: [...followingIds, new Types.ObjectId(userId)] } })
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('userId', 'username avatar');

    return posts;
  }

  // Follower methods
  async followUser(followerId: string, followingId: string): Promise<void> {
    if (followerId === followingId) {
      throw new BadRequestException('Cannot follow yourself');
    }

    const existingFollow = await this.followerModel.findOne({
      followerId: new Types.ObjectId(followerId),
      followingId: new Types.ObjectId(followingId),
    });

    if (existingFollow) {
      throw new BadRequestException('Already following this user');
    }

    const follow = await this.followerModel.create({
      followerId: new Types.ObjectId(followerId),
      followingId: new Types.ObjectId(followingId),
    });

    await this.createNotification(followingId, followerId, 'follow');
    this.socialGateway.emitNewFollow(followingId, follow);
  }

  async unfollowUser(followerId: string, followingId: string): Promise<void> {
    const follow = await this.followerModel.findOneAndDelete({
      followerId: new Types.ObjectId(followerId),
      followingId: new Types.ObjectId(followingId),
    });

    if (!follow) {
      throw new NotFoundException('Follow relationship not found');
    }

    this.socialGateway.emitUnfollow(followingId, followerId);
  }

  async getFollowers(userId: string): Promise<string[]> {
    const followers = await this.followerModel.find({
      followingId: new Types.ObjectId(userId),
    });
    return followers.map(f => f.followerId.toString());
  }

  async getFollowing(userId: string): Promise<string[]> {
    const following = await this.followerModel.find({
      followerId: new Types.ObjectId(userId),
    });
    return following.map(f => f.followingId.toString());
  }

  private async updateFollowersFeeds(userId: string, postId: Types.ObjectId): Promise<void> {
    const followers = await this.getFollowers(userId);
    for (const follower of followers) {
      await this.userFeedModel.updateOne(
        { userId: new Types.ObjectId(follower) },
        {
          $push: {
            posts: {
              postId: postId,
              addedAt: new Date()
            }
          }
        },
        { upsert: true }
      );
    }
  }

  // Sharing methods
  async sharePost(userId: string, postId: string, shareContent?: string): Promise<SocialPost> {
    const post = await this.socialPostModel.findById(postId);
    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const share = await this.userInteractionModel.create({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'SHARE',
      content: shareContent,
    });

    post.shares += 1;
    await post.save();

    await this.createNotification(
      post.userId.toString(),
      userId,
      'share',
      postId
    );
    this.socialGateway.emitNewShare(postId, share);

    return post;
  }

  // Notification methods
  async createNotification(
    recipientId: string,
    senderId: string,
    type: string,
    postId?: string,
    commentId?: string,
    mentionId?: string
  ): Promise<Notification> {
    return this.notificationModel.create({
      recipientId: new Types.ObjectId(recipientId),
      senderId: new Types.ObjectId(senderId),
      type,
      postId: postId ? new Types.ObjectId(postId) : undefined,
      commentId: commentId ? new Types.ObjectId(commentId) : undefined,
      mentionId: mentionId ? new Types.ObjectId(mentionId) : undefined,
      read: false,
    });
  }

  async getNotifications(userId: string, page: number = 1, limit: number = 20): Promise<Notification[]> {
    return this.notificationModel
      .find({ recipientId: new Types.ObjectId(userId) })
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('senderId', 'username avatar');
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    const notification = await this.notificationModel.findByIdAndUpdate(
      notificationId,
      { read: true },
      { new: true }
    );

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }
  }

  async markAllNotificationsAsRead(userId: string): Promise<void> {
    await this.notificationModel.updateMany(
      { recipientId: new Types.ObjectId(userId), read: false },
      { read: true }
    );
  }

  async updateNotificationPreferences(preferences: NotificationPreferencesDto) {
    // TODO: Implement notification preferences update
    console.log('Updating notification preferences:', preferences);
  }

  async addReaction(userId: string, postId: string, reactionType: ReactionType) {
    const post = await this.socialPostModel.findById(postId);
    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const existingReaction = await this.userInteractionModel.findOne({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'REACTION',
    });

    if (existingReaction) {
      existingReaction.reactionType = reactionType;
      await existingReaction.save();
      this.socialGateway.emitReactionUpdate(postId, existingReaction);
      return existingReaction;
    }

    const reaction = await this.userInteractionModel.create({
      userId: new Types.ObjectId(userId),
      postId: new Types.ObjectId(postId),
      type: 'REACTION',
      reactionType,
    });

    await this.createNotification(
      post.userId.toString(),
      userId,
      'reaction',
      postId
    );
    this.socialGateway.emitNewReaction(postId, reaction);

    return reaction;
  }

  async mentionUsers(userId: string, mentionData: { userIds: string[]; message: string }) {
    const mentions = await Promise.all(
      mentionData.userIds.map(async (targetUserId) => {
        const mention = await this.userInteractionModel.create({
          userId: new Types.ObjectId(userId),
          targetUserId: new Types.ObjectId(targetUserId),
          type: 'MENTION',
          content: mentionData.message,
        });

        if (mention._id) {
          await this.createNotification(
            targetUserId,
            userId,
            'mention',
            undefined,
            undefined,
            mention._id.toString()
          );
          this.socialGateway.emitNewMention(targetUserId, mention);
        }

        return mention;
      })
    );

    return mentions;
  }

  private async notifyFollowers(userId: string, type: string, postId: string) {
    const followers = await this.followerModel.find({ followingId: new Types.ObjectId(userId) });
    
    await Promise.all(
      followers.map(follower =>
        this.createNotification(
          follower.followerId.toString(),
          userId,
          type,
          postId
        )
      )
    );
  }
}