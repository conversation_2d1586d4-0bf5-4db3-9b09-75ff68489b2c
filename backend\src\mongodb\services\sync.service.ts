import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SocialPost } from '../schemas/social-post.schema';
import { Notification } from '../schemas/notification.schema';
import { Follower } from '../schemas/follower.schema';
import { SocialGateway } from '../gateways/social.gateway';
import { UserInteraction } from '../schemas/user-interaction.schema';

@Injectable()
export class SyncService {
  constructor(
    @InjectModel(SocialPost.name) private socialPostModel: Model<SocialPost>,
    @InjectModel(Notification.name) private notificationModel: Model<Notification>,
    @InjectModel(Follower.name) private followerModel: Model<Follower>,
    private readonly socialGateway: SocialGateway,
  ) {}

  async syncPost(post: SocialPost) {
    const followers = await this.getPostAuthorFollowers(post.userId.toString());
    
    await Promise.all(
      followers.map(async (followerId) => {
        await this.notificationModel.create({
          recipientId: new Types.ObjectId(followerId),
          senderId: post.userId,
          type: 'post',
          postId: post._id,
          read: false,
        });

        this.socialGateway.emitNewPost(post.userId.toString(), post);
      })
    );
  }

  async syncComment(comment: UserInteraction) {
    const post = await this.socialPostModel.findById(comment.postId);
    if (!post) return;

    await this.notificationModel.create({
      recipientId: post.userId,
      senderId: comment.userId,
      type: 'comment',
      postId: post._id,
      commentId: comment._id,
      read: false,
    });

    if (post._id) {
      this.socialGateway.emitNewComment(post._id.toString(), comment);
    }
  }

  async syncLike(like: UserInteraction) {
    const post = await this.socialPostModel.findById(like.postId);
    if (!post) return;

    await this.notificationModel.create({
      recipientId: post.userId,
      senderId: like.userId,
      type: 'like',
      postId: post._id,
      read: false,
    });

    if (post._id) {
      this.socialGateway.emitNewLike(post._id.toString(), like);
    }
  }

  async syncFollow(follow: Follower) {
    await this.notificationModel.create({
      recipientId: follow.followingId,
      senderId: follow.followerId,
      type: 'follow',
      read: false,
    });

    this.socialGateway.emitNewFollow(follow.followingId.toString(), follow);
  }

  async syncMention(mention: UserInteraction) {
    if (!mention.targetUserId) {
      console.error('Mention missing targetUserId:', mention);
      return;
    }

    await this.notificationModel.create({
      recipientId: mention.targetUserId,
      senderId: mention.userId,
      type: 'mention',
      mentionId: mention._id,
      read: false,
    });

    this.socialGateway.emitNewMention(mention.targetUserId.toString(), mention);
  }

  private async getPostAuthorFollowers(userId: string): Promise<string[]> {
    const followers = await this.followerModel.find({ 
      followingId: new Types.ObjectId(userId) 
    });
    return followers.map(f => f.followerId.toString());
  }
} 