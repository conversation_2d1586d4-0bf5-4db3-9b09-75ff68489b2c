<<<<<<< HEAD
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
export class Notification {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  message!: string;

  @Column()
  createdAt: Date = new Date();
=======
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
export class Notification {
  @PrimaryGeneratedColumn()
  id: number = 0;

  @Column()
  message: string = '';

  @Column()
  createdAt: Date = new Date();
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
}