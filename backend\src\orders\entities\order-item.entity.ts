import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, ManyToOne, Column, Join<PERSON><PERSON>umn } from 'typeorm';
import { Order } from './order.entity';
import { Product } from '../../products/entities/product.entity';

@Entity('order_items')
export class OrderItem {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Order, order => order.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order!: Order;

  @Column({ name: 'order_id' })
  orderId!: string;

  @ManyToOne(() => Product, { eager: true })
  @JoinColumn({ name: 'product_id' })
  product!: Product;

  @Column({ name: 'product_id' })
  productId!: string;

  @Column('int')
  quantity!: number;

  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice!: number;

  @Column('decimal', { precision: 12, scale: 2 })
  totalPrice!: number;

  @Column({ nullable: true })
  productName?: string; // Store product name at time of order

  @Column({ nullable: true })
  productSku?: string; // Store SKU at time of order
}
