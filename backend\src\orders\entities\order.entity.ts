import { Entity, PrimaryGeneratedColumn, ManyToOne, Column, CreateDateColumn, UpdateDateColumn, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrderItem } from './order-item.entity';

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user!: User;

  @Column({ name: 'user_id' })
  userId!: string;

  @OneToMany(() => OrderItem, orderItem => orderItem.order, { cascade: true, eager: true })
  items!: OrderItem[];

  @Column('decimal', { precision: 12, scale: 2 })
  subtotal!: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  tax!: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  shipping!: number;

  @Column('decimal', { precision: 12, scale: 2 })
  total!: number;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING
  })
  status!: OrderStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING
  })
  paymentStatus!: PaymentStatus;

  @Column({ nullable: true })
  paymentMethod?: string;

  @Column({ nullable: true })
  paymentIntentId?: string;

  @Column('text', { nullable: true })
  shippingAddress?: string;

  @Column({ nullable: true })
  contactPhone?: string;

  @Column('text', { nullable: true })
  notes?: string;

  @Column({ nullable: true })
  trackingNumber?: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}