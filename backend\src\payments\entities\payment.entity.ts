import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { Order } from '../../orders/entities/order.entity';

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  PAYPAL = 'paypal',
  BANK_TRANSFER = 'bank_transfer',
  CASH_ON_DELIVERY = 'cash_on_delivery',
  DIGITAL_WALLET = 'digital_wallet',
}

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Order, { eager: true })
  @JoinColumn({ name: 'order_id' })
  order!: Order;

  @Column({ name: 'order_id' })
  orderId!: string;

  @Column('decimal', { precision: 12, scale: 2 })
  amount!: number;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING
  })
  status!: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.CREDIT_CARD
  })
  paymentMethod!: PaymentMethod;

  @Column({ length: 3, default: 'USD' })
  currency!: string;

  @Column({ nullable: true })
  paymentIntentId?: string;

  @Column({ nullable: true })
  transactionId?: string;

  @Column({ nullable: true })
  gatewayResponse?: string;

  @Column({ nullable: true })
  failureReason?: string;

  @Column({ nullable: true })
  originalPaymentId?: string; // For refunds

  @Column({ nullable: true })
  processedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Additional fields for payment metadata
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @Column({ nullable: true })
  customerEmail?: string;

  @Column({ nullable: true })
  billingAddress?: string;

  @Column({ nullable: true })
  last4Digits?: string; // Last 4 digits of card

  @Column({ nullable: true })
  cardBrand?: string; // Visa, Mastercard, etc.
}
