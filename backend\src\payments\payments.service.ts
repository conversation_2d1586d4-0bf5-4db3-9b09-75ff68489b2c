import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Payment, PaymentStatus, PaymentMethod } from './entities/payment.entity';
import { Order, OrderStatus, PaymentStatus as OrderPaymentStatus } from '../orders/entities/order.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
  ) {}

  async createPaymentIntent(createPaymentDto: CreatePaymentDto): Promise<{ clientSecret: string; paymentId: string }> {
    const { orderId, amount, paymentMethod } = createPaymentDto;

    // Validate order exists
    const order = await this.orderRepository.findOne({ where: { id: orderId } });
    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    // Validate amount matches order total
    if (Number(amount) !== Number(order.total)) {
      throw new BadRequestException(`Payment amount ${amount} does not match order total ${order.total}`);
    }

    // Create payment record
    const payment = this.paymentRepository.create({
      orderId,
      amount: Number(amount),
      paymentMethod,
      status: PaymentStatus.PENDING,
      currency: 'USD', // Default currency
    });

    const savedPayment = await this.paymentRepository.save(payment);

    // Mock payment intent creation (replace with real payment processor)
    const clientSecret = this.generateMockClientSecret(savedPayment.id);

    // Update payment with intent ID
    await this.paymentRepository.update(savedPayment.id, {
      paymentIntentId: clientSecret,
    });

    return {
      clientSecret,
      paymentId: savedPayment.id,
    };
  }

  async confirmPayment(paymentId: string, paymentIntentId: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
      relations: ['order'],
    });

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${paymentId} not found`);
    }

    if (payment.paymentIntentId !== paymentIntentId) {
      throw new BadRequestException('Invalid payment intent ID');
    }

    // Mock payment confirmation (replace with real payment processor verification)
    const isPaymentSuccessful = this.mockPaymentConfirmation(paymentIntentId);

    if (isPaymentSuccessful) {
      // Update payment status
      await this.paymentRepository.update(paymentId, {
        status: PaymentStatus.COMPLETED,
        processedAt: new Date(),
      });

      // Update order payment status
      await this.orderRepository.update(payment.orderId, {
        paymentStatus: OrderPaymentStatus.PAID,
        status: OrderStatus.CONFIRMED,
      });

      return this.paymentRepository.findOne({
        where: { id: paymentId },
        relations: ['order'],
      });
    } else {
      // Update payment status to failed
      await this.paymentRepository.update(paymentId, {
        status: PaymentStatus.FAILED,
        failureReason: 'Payment declined by mock processor',
      });

      throw new BadRequestException('Payment failed');
    }
  }

  async refundPayment(paymentId: string, amount?: number): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
      relations: ['order'],
    });

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${paymentId} not found`);
    }

    if (payment.status !== PaymentStatus.COMPLETED) {
      throw new BadRequestException('Can only refund completed payments');
    }

    const refundAmount = amount || payment.amount;
    if (refundAmount > payment.amount) {
      throw new BadRequestException('Refund amount cannot exceed original payment amount');
    }

    // Mock refund processing (replace with real payment processor)
    const isRefundSuccessful = this.mockRefundProcessing(payment.paymentIntentId, refundAmount);

    if (isRefundSuccessful) {
      // Create refund record
      const refund = this.paymentRepository.create({
        orderId: payment.orderId,
        amount: -refundAmount, // Negative amount for refund
        paymentMethod: payment.paymentMethod,
        status: PaymentStatus.REFUNDED,
        currency: payment.currency,
        originalPaymentId: paymentId,
        paymentIntentId: `refund_${Date.now()}`,
        processedAt: new Date(),
      });

      await this.paymentRepository.save(refund);

      // Update original payment if full refund
      if (refundAmount === payment.amount) {
        await this.paymentRepository.update(paymentId, {
          status: PaymentStatus.REFUNDED,
        });

        // Update order status
        await this.orderRepository.update(payment.orderId, {
          paymentStatus: OrderPaymentStatus.REFUNDED,
          status: OrderStatus.REFUNDED,
        });
      }

      return refund;
    } else {
      throw new BadRequestException('Refund processing failed');
    }
  }

  async getPaymentsByOrder(orderId: string): Promise<Payment[]> {
    return this.paymentRepository.find({
      where: { orderId },
      order: { createdAt: 'DESC' },
    });
  }

  async getPayment(paymentId: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
      relations: ['order'],
    });

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${paymentId} not found`);
    }

    return payment;
  }

  // Mock methods (replace with real payment processor integration)
  private generateMockClientSecret(paymentId: string): string {
    return `pi_mock_${paymentId}_secret_${Date.now()}`;
  }

  private mockPaymentConfirmation(paymentIntentId: string): boolean {
    // Mock 90% success rate
    return Math.random() > 0.1;
  }

  private mockRefundProcessing(paymentIntentId: string, amount: number): boolean {
    // Mock 95% success rate for refunds
    return Math.random() > 0.05;
  }
}
