<<<<<<< HEAD
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity()
export class Post {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  title!: string;

  @Column('text')
  content!: string;

  @ManyToOne(() => User, user => user.posts)
  author!: User;

  @Column()
  authorId!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ default: 0 })
  likes!: number;

  @Column({ default: 0 })
  shares!: number;

  @Column({ default: false })
  isPublished!: boolean;
}
=======
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity()
export class Post {
  @PrimaryGeneratedColumn()
  id: number = 0;

  @Column()
  title: string = '';

  @Column()
  content: string = '';

  @Column({ nullable: true })
  userId: string = '';

  @CreateDateColumn()
  createdAt: Date = new Date();
}
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
