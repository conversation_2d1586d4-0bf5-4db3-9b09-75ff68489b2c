import { Test, TestingModule } from '@nestjs/testing';
import { PostsController } from './posts.controller';
import { PostsService } from './posts.service';
import { mockPost } from '../test/test-utils';

describe('PostsController', () => {
  let controller: PostsController;
  let service: jest.Mocked<PostsService>;

  beforeEach(async () => {
    const mockPostsService = {
      findAll: jest.fn().mockResolvedValue([mockPost]),
      findOne: jest.fn().mockResolvedValue(mockPost),
      create: jest.fn().mockResolvedValue(mockPost),
      update: jest.fn().mockResolvedValue(mockPost),
      remove: jest.fn().mockResolvedValue({ affected: 1 }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PostsController],
      providers: [
        {
          provide: PostsService,
          useValue: mockPostsService,
        },
      ],
    }).compile();

    controller = module.get<PostsController>(PostsController);
    service = module.get(PostsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should find all posts', async () => {
    expect(await controller.findAll()).toEqual([mockPost]);
    expect(service.findAll).toHaveBeenCalled();
  });

  it('should find one post', async () => {
    expect(await controller.findOne('1')).toEqual(mockPost);
    expect(service.findOne).toHaveBeenCalledWith('1');
  });
});
