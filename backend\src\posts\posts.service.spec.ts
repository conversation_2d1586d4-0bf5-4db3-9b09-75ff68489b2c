import { Test, TestingModule } from '@nestjs/testing';
import { PostsService } from './posts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Post } from './entities/post.entity';
import { createMockRepository, mockPost } from '../test/test-utils';

describe('PostsService', () => {
  let service: PostsService;
  let postRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    postRepository = createMockRepository();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: getRepositoryToken(Post),
          useValue: postRepository,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find all posts', async () => {
    postRepository.find.mockResolvedValueOnce([mockPost]);
    const result = await service.findAll();
    expect(result).toEqual([mockPost]);
  });

  it('should find one post', async () => {
    postRepository.findOne.mockResolvedValueOnce(mockPost);
    const result = await service.findById('1');
    expect(result).toEqual(mockPost);
  });
});
