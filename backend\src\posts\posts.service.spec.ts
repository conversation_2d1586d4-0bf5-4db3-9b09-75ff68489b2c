<<<<<<< HEAD
import { Test, TestingModule } from '@nestjs/testing';
import { PostsService } from './posts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Post } from './entities/post.entity';
import { createMockRepository, mockPost } from '../test/test-utils';

describe('PostsService', () => {
  let service: PostsService;
  let postRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    postRepository = createMockRepository();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: getRepositoryToken(Post),
          useValue: postRepository,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find all posts', async () => {
    const mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([mockPost]),
    };
    postRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
    const result = await service.findAll();
    expect(result).toEqual([mockPost]);
  });

  it('should find one post', async () => {
    postRepository.findOne.mockResolvedValueOnce(mockPost);
    const result = await service.findById('1');
    expect(result).toEqual(mockPost);
  });
});
=======
import { Test, TestingModule } from '@nestjs/testing';
import { PostsService } from './posts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Post } from './entities/post.entity';

const mockPostRepository = {
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
};

describe('PostsService', () => {
  let service: PostsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: getRepositoryToken(Post),
          useValue: mockPostRepository,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
