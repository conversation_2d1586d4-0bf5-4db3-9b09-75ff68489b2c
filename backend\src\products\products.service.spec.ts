
import { Test, TestingModule } from '@nestjs/testing';
import { ProductsService } from './products.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { Category } from './entities/category.entity';
import { ProductImage } from './entities/product-image.entity';
import { createMockRepository, mockProduct } from '../test/test-utils';

describe('ProductsService', () => {
  let service: ProductsService;
  let productRepository: ReturnType<typeof createMockRepository>;
  let categoryRepository: ReturnType<typeof createMockRepository>;
  let productImageRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    productRepository = createMockRepository();
    categoryRepository = createMockRepository();
    productImageRepository = createMockRepository();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: getRepositoryToken(Product),
          useValue: productRepository,
        },
        {
          provide: getRepositoryToken(Category),
          useValue: categoryRepository,
        },
        {
          provide: getRepositoryToken(ProductImage),
          useValue: productImageRepository,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find all products', async () => {
    const mockQueryBuilder = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[mockProduct], 1]),
    };
    productRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
    const result = await service.findAll({});
    expect(result.products).toEqual([mockProduct]);
    expect(result.totalPages).toBe(1);
  });

  it('should find one product', async () => {
    productRepository.findOne.mockResolvedValueOnce(mockProduct);
    const result = await service.findOne('1');
    expect(result).toEqual(mockProduct);
  });
});