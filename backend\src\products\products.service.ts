import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { FilterProductsDto } from './dto/filter-products.dto';  // Updated import path
import { Product } from './entities/product.entity';
import { ProductImage } from './entities/product-image.entity';
import { User } from '../users/entities/user.entity';
import { Category } from '../categories/entities/category.entity';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(ProductImage)
    private productImageRepository: Repository<ProductImage>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
  ) {}

  async create(createProductDto: CreateProductDto, user: User): Promise<Product> {
    const product = this.productRepository.create({
      title: createProductDto.title,
      description: createProductDto.description,
      price: createProductDto.price,
      stock: createProductDto.stock,
      seller: user,
      category: { id: createProductDto.categoryId } as any,
    });

    const savedProduct = await this.productRepository.save(product);

    if (createProductDto.images && createProductDto.images.length > 0) {
      const productImages = createProductDto.images.map(url =>
        this.productImageRepository.create({ url, product: savedProduct }),
      );
      savedProduct.images = await this.productImageRepository.save(productImages);
    } else {
      savedProduct.images = [];
    }

    return savedProduct;
  }

  async findAll(filterProductsDto?: FilterProductsDto): Promise<{ products: Product[]; totalPages: number }> {
    const query = this.productRepository.createQueryBuilder('product')
      .leftJoinAndSelect('product.seller', 'seller')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('product.images', 'images');

    if (filterProductsDto) {
      const { search } = filterProductsDto;
      
      if (search) {
        query.where('product.title LIKE :search OR product.description LIKE :search', {
          search: `%${search}%`,
        });
      }
      
      // Apply sorting based on the sort parameter
      if (filterProductsDto.sort === 'popular') {
        query.orderBy('product.views', 'DESC');
      } else if (filterProductsDto.sort === 'newest') {
        query.orderBy('product.createdAt', 'DESC');
      } else if (filterProductsDto.sort === 'price-low') {
        query.orderBy('product.price', 'ASC');
      } else if (filterProductsDto.sort === 'price-high') {
        query.orderBy('product.price', 'DESC');
      }

      // Filter by seller if userId is provided
      if (filterProductsDto.userId) {
        query.andWhere('seller.id = :userId', { userId: filterProductsDto.userId });
      }
    }

    const [products, total] = await query.getManyAndCount();
    const limit = filterProductsDto?.limit || 10;
    const totalPages = Math.ceil(total / limit);

    return { products, totalPages };
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['seller', 'category', 'images'],
    });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, user: User): Promise<Product> {
    const product = await this.findOne(id);
    
    if (product.seller.id !== user.id) {
      throw new BadRequestException('You can only update your own products');
    }
    
    Object.assign(product, updateProductDto);
    return this.productRepository.save(product);
  }

  async remove(id: string, user: User): Promise<void> {
    const product = await this.findOne(id);
    
    if (product.seller.id !== user.id) {
      throw new BadRequestException('You can only delete your own products');
    }
    
    await this.productRepository.remove(product);
  }

  /**
   * Get all categories from the database
   * @returns Promise<Category[]> - Array of all categories
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      return await this.categoryRepository.find({
        order: {
          name: 'ASC'
        }
      });
    } catch (error) {
      throw new BadRequestException('Failed to fetch categories');
    }
  }
}