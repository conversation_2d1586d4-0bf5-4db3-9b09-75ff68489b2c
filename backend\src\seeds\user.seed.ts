import * as bcrypt from 'bcryptjs';
import { DataSource } from 'typeorm';
import { User } from '../users/entities/user.entity'; // Adjust the path based on your project structure

export async function seedUsers(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);

  const existingUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });

  if (!existingUser) {
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('admin123', saltRounds);

    const adminUser = userRepository.create({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      fullName: 'Admin User',
      businessName: 'Market O\'Clock',
      firstName: 'Admin',
      lastName: 'User',
    });

    await dataSource.manager.save(adminUser);
    console.log('Admin user seeded successfully!');
  } else {
    console.log('Admin user already exists.');
  }
}