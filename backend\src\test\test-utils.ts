import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Product } from '../products/entities/product.entity';
import { Post } from '../microblog/entities/post.entity';
import { Comment } from '../microblog/entities/comment.entity';

export const mockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findAndCount: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  createQueryBuilder: jest.fn(),
});

export const createMockRepository = <T = any>(): jest.Mocked<Repository<T>> => {
  return mockRepository() as unknown as jest.Mocked<Repository<T>>;
};

export type MockType<T> = {
  [P in keyof T]?: jest.Mock<any>;
};

export const mockUser: Partial<User> = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'user',
  username: 'testuser'
};

export const mockProduct: Partial<Product> = {
  id: '1',
  title: 'Test Product',
  description: 'Test Description',
  price: 100
};

export const mockPost: Partial<Post> = {
  id: '1',
  content: 'Test Content'
};

export const mockComment: Partial<Comment> = {
  id: '1',
  content: 'Test Comment'
};
