<<<<<<< HEAD
import { Entity, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Product } from '../../products/entities/product.entity';
import { Post } from '../../microblog/entities/post.entity';
import { Comment } from '../../microblog/entities/comment.entity';
import { Like } from '../../microblog/entities/like.entity';
import { ProductImage } from '../../products/entities/product-image.entity';
import { Category } from '../../products/entities/category.entity';
import { JoinColumn } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column()
  role: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ unique: true })
  username: string;

  @Column({ nullable: true })
  fullName: string;

  @Column({ nullable: true })
  businessName: string;

  @Column({ name: 'seller_id', nullable: true })
  sellerId: string;

  @OneToMany(() => Product, (product) => product.seller)
  products: Product[];

  @OneToMany(() => Post, (post) => post.author)
  posts: Post[];

  @OneToMany(() => Comment, (comment) => comment.author)
  comments: Comment[];

  @OneToMany(() => Like, (like) => like.user)
  likes: Like[];

  @ManyToOne(() => Category)
  category: Category;

  @OneToMany(() => ProductImage, (image) => image.product, { cascade: true })
  images: ProductImage[];

  @ManyToOne(() => User, (user) => user.products)
  @JoinColumn({ name: 'seller_id' })
  seller: User;

  @Column({ nullable: true })
  thumbnailUrl: string;

  @Column('decimal', { precision: 3, scale: 2, nullable: true })
  rating: number;

  @Column('int', { default: 0 })
  ratingCount: number;

  @Column('int', { default: 0 })
  viewCount: number;

  @Column('int', { default: 0 })
  purchaseCount: number;

  @Column('int', { default: 0 })
  wishlistCount: number;

  @Column('int', { default: 0 })
  commentCount: number;

  @Column('int', { default: 0 })
  shareCount: number;

  constructor(partial: Partial<User> = {}) {
    Object.assign(this, partial);
  }
=======
import { Entity, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Product } from '../../products/entities/product.entity';
import { Post } from '../../microblog/entities/post.entity';
import { Comment } from '../../microblog/entities/comment.entity';
import { Like } from '../../microblog/entities/like.entity';
import { ProductImage } from '../../products/entities/product-image.entity';
import { Category } from '../../products/entities/category.entity';
import { JoinColumn } from 'typeorm';


@Entity('user')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ unique: true })
  username!: string;

  @Column({ unique: true })
  email!: string;

  @Column()
  password_hash!: string;

  @Column()
  role!: string;

  @Column({ nullable: true })
  full_name!: string;

  @Column({ nullable: true })
  business_name?: string;

  @Column({ name: 'seller_id', nullable: true })
  sellerId?: string;

  @OneToMany(() => Product, (product) => product.seller)
  products!: Product[];

  @OneToMany(() => Post, (post) => post.author)
  posts!: Post[];

  @OneToMany(() => Comment, (comment) => comment.author)
  comments!: Comment[];

  @OneToMany(() => Like, (like) => like.user)
  likes!: Like[];

  @ManyToOne(() => Category)
  category?: Category;

  @OneToMany(() => ProductImage, (image) => image.product, { cascade: true })
  images?: ProductImage[];

  @ManyToOne(() => User, (user) => user.products)
  @JoinColumn({ name: 'seller_id' }) // Explicitly define the foreign key column
  seller?: User;

  @Column({ nullable: true })
  thumbnailUrl?: string;

  @Column('decimal', { precision: 3, scale: 2, nullable: true, default: 0 })
  rating?: number;

  // Removed duplicate password field - we use password_hash instead

  @Column('int', { default: 0 })
  ratingCount!: number;

  @Column('int', { default: 0 })
  viewCount!: number;

  @Column('int', { default: 0 })
  purchaseCount!: number;

  @Column('int', { default: 0 })
  wishlistCount!: number;

  @Column('int', { default: 0 })
  commentCount!: number;

  @Column('int', { default: 0 })
  shareCount!: number;

  @Column({ default: false })
  is_verified!: boolean;

  @Column({ nullable: true })
  verification_token?: string;

  @Column({ nullable: true })
  reset_token?: string;

  @Column({ nullable: true })
  reset_token_expiry?: Date;

  @Column({ default: 0 })
  failed_login_attempts!: number;

  @Column({ nullable: true })
  last_failed_login?: Date;

  @Column({ default: false })
  is_locked!: boolean;

  @Column({ nullable: true })
  lock_expires_at?: Date;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
>>>>>>> d7829e4 (Fix helmet import, update backend production setup, and stage all changes)
}