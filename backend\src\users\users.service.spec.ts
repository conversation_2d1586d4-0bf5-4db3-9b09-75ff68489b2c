import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { createMockRepository, mockUser } from '../test/test-utils';

describe('UsersService', () => {
  let service: UsersService;
  let userRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    userRepository = createMockRepository();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: userRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find one user', async () => {
    userRepository.findOne.mockResolvedValueOnce(mockUser);
    const result = await service.findOne('1');
    expect(result).toEqual(mockUser);
  });

  it('should find all users', async () => {
    userRepository.find.mockResolvedValueOnce([mockUser]);
    const result = await service.findAll();
    expect(result).toEqual([mockUser]);
  });
});
