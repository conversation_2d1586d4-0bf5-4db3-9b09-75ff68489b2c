const axios = require('axios');

const API_URL = 'http://localhost:3000';
const TEST_USER = {
  username: 'testuser' + Math.floor(Math.random() * 10000), // Add random number to avoid conflicts
  email: `testuser${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'Password123!',
  role: 'user',
  fullName: 'Test User',
  businessName: 'Test Business'
};

let authToken = '';

async function testAuthEndpoints() {
  console.log('=== AUTHENTICATION ENDPOINTS TEST ===');
  console.log('Testing with user:', TEST_USER);
  
  try {
    // Step 1: Test the unprotected endpoint
    console.log('\n1. Testing unprotected endpoint: /auth-test');
    try {
      const unprotectedResponse = await axios.get(`${API_URL}/auth-test`);
      console.log('✅ SUCCESS: Unprotected endpoint accessible');
      console.log('Response:', unprotectedResponse.data);
    } catch (error) {
      console.log('❌ FAILED: Unprotected endpoint test');
      console.error('Error:', error.response?.data || error.message);
    }
    
    // Step 2: Register a new user
    console.log('\n2. Testing registration: POST /auth/register');
    try {
      const registerResponse = await axios.post(`${API_URL}/auth/register`, TEST_USER);
      console.log('✅ SUCCESS: User registration');
      console.log('User ID:', registerResponse.data.user.id);
      console.log('Username:', registerResponse.data.user.username);
      console.log('Email:', registerResponse.data.user.email);
      console.log('Role:', registerResponse.data.user.role);
      
      // Save the token for later use
      authToken = registerResponse.data.token;
      console.log('Token received:', authToken ? 'Yes' : 'No');
    } catch (error) {
      console.log('❌ FAILED: User registration');
      console.error('Error:', error.response?.data || error.message);
      
      // If registration fails, try logging in (user might already exist)
      console.log('\nAttempting login instead...');
      await testLogin();
    }
    
    // Step 3: Test the protected endpoint
    console.log('\n3. Testing protected endpoint: GET /auth-protected');
    try {
      const protectedResponse = await axios.get(`${API_URL}/auth-protected`, {
        headers: {
          Authorization: `Bearer ${authToken}`
        }
      });
      console.log('✅ SUCCESS: Protected endpoint accessible with token');
      console.log('Response:', protectedResponse.data);
    } catch (error) {
      console.log('❌ FAILED: Protected endpoint test');
      console.error('Error:', error.response?.data || error.message);
    }
    
    // Step 4: Test the /auth/me endpoint
    console.log('\n4. Testing user profile: GET /auth/me');
    try {
      const meResponse = await axios.get(`${API_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${authToken}`
        }
      });
      console.log('✅ SUCCESS: User profile accessible');
      console.log('User profile:', meResponse.data);
    } catch (error) {
      console.log('❌ FAILED: User profile test');
      console.error('Error:', error.response?.data || error.message);
    }
    
    // Step 5: Test accessing protected endpoint without token
    console.log('\n5. Testing protected endpoint without token');
    try {
      await axios.get(`${API_URL}/auth-protected`);
      console.log('❌ FAILED: Protected endpoint accessible without token!');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ SUCCESS: Protected endpoint correctly rejected unauthorized access');
      } else {
        console.log('❓ UNEXPECTED: Protected endpoint test without token');
        console.error('Error:', error.response?.data || error.message);
      }
    }
    
    console.log('\n=== AUTHENTICATION TEST SUMMARY ===');
    console.log('Authentication system appears to be working correctly!');
    console.log('JWT token generation and verification is functioning.');
    console.log('Protected routes are properly secured.');
    console.log('User registration and profile access are working.');
    
  } catch (error) {
    console.error('\n❌ TEST FAILED with error:', error.message);
  }
}

async function testLogin() {
  console.log('\nTesting login: POST /auth/login');
  try {
    const loginData = {
      email: TEST_USER.email,
      password: TEST_USER.password
    };
    
    const loginResponse = await axios.post(`${API_URL}/auth/login`, loginData);
    console.log('✅ SUCCESS: User login');
    console.log('User ID:', loginResponse.data.user.id);
    console.log('Username:', loginResponse.data.user.username);
    console.log('Email:', loginResponse.data.user.email);
    
    // Save the token for later use
    authToken = loginResponse.data.token;
    console.log('Token received:', authToken ? 'Yes' : 'No');
    return true;
  } catch (error) {
    console.log('❌ FAILED: User login');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

// Run the tests
testAuthEndpoints();
