const axios = require('axios');

const API_URL = 'http://localhost:3000';

async function testAuth() {
  try {
    console.log('Testing authentication system...');
    
    // Test the unprotected endpoint
    console.log('\n1. Testing unprotected endpoint:');
    const unprotectedResponse = await axios.get(`${API_URL}/auth-test`);
    console.log('Response:', unprotectedResponse.data);
    
    // Register a new user
    console.log('\n2. Registering a new user:');
    const registerData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
      fullName: 'Test User',
      businessName: 'Test Business'
    };
    
    let token;
    try {
      const registerResponse = await axios.post(`${API_URL}/auth/register`, registerData);
      console.log('Registration successful!');
      console.log('User:', registerResponse.data.user);
      token = registerResponse.data.token;
      console.log('Token:', token);
    } catch (error) {
      console.log('Registration failed, trying login instead (user might already exist)');
      
      // Try logging in
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };
      
      const loginResponse = await axios.post(`${API_URL}/auth/login`, loginData);
      console.log('Login successful!');
      console.log('User:', loginResponse.data.user);
      token = loginResponse.data.token;
      console.log('Token:', token);
    }
    
    // Test the protected endpoint
    console.log('\n3. Testing protected endpoint:');
    try {
      const protectedResponse = await axios.get(`${API_URL}/auth-protected`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      console.log('Protected endpoint access successful!');
      console.log('Response:', protectedResponse.data);
    } catch (error) {
      console.error('Protected endpoint access failed:', error.response?.data || error.message);
    }
    
    // Test the /auth/me endpoint
    console.log('\n4. Testing /auth/me endpoint:');
    try {
      const meResponse = await axios.get(`${API_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      console.log('User profile access successful!');
      console.log('User profile:', meResponse.data);
    } catch (error) {
      console.error('User profile access failed:', error.response?.data || error.message);
    }
    
    console.log('\nAuthentication system test completed!');
  } catch (error) {
    console.error('Error during authentication test:', error.response?.data || error.message);
  }
}

testAuth();
