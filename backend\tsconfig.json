{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"]}