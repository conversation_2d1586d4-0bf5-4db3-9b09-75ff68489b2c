-- Seed: 001_initial_data.sql
-- Description: Initial seed data for Market O'Clock

-- Insert admin user
INSERT INTO users (email, password_hash, first_name, last_name, role)
VALUES (
    '<EMAIL>',
    -- This is a hashed version of 'admin123' - in production, use proper password hashing
    '$2a$10$rDkPvvAFV6GgJkKqGxw4Ue6QZQZQZQZQZQZQZQZQZQZQZQZQZQZQ',
    'Admin',
    'User',
    'admin'
);

-- Insert sample categories
INSERT INTO categories (name, slug, description) VALUES
('Electronics', 'electronics', 'Electronic devices and accessories'),
('Clothing', 'clothing', 'Fashion and apparel'),
('Home & Garden', 'home-garden', 'Home decor and garden supplies'),
('Books', 'books', 'Books and publications');

-- Insert subcategories
INSERT INTO categories (name, slug, description, parent_id)
SELECT 'Smartphones', 'smartphones', 'Mobile phones and accessories', id
FROM categories WHERE slug = 'electronics';

INSERT INTO categories (name, slug, description, parent_id)
SELECT 'Laptops', 'laptops', 'Portable computers and accessories', id
FROM categories WHERE slug = 'electronics';

-- Insert sample vendor
INSERT INTO users (email, password_hash, first_name, last_name, role)
VALUES (
    '<EMAIL>',
    -- This is a hashed version of 'vendor123' - in production, use proper password hashing
    '$2a$10$rDkPvvAFV6GgJkKqGxw4Ue6QZQZQZQZQZQZQZQZQZQZQZQZQZQZQ',
    'Sample',
    'Vendor',
    'vendor'
);

INSERT INTO vendor_profiles (user_id, business_name, description, status)
SELECT id, 'Tech Store', 'Your one-stop shop for electronics', 'active'
FROM users WHERE email = '<EMAIL>';

-- Insert sample products
INSERT INTO products (vendor_id, category_id, name, slug, description, price, stock_quantity, status)
SELECT 
    vp.id,
    c.id,
    'iPhone 13 Pro',
    'iphone-13-pro',
    'Latest iPhone with amazing camera and performance',
    999.99,
    50,
    'published'
FROM vendor_profiles vp
CROSS JOIN categories c
WHERE vp.business_name = 'Tech Store'
AND c.slug = 'smartphones';

INSERT INTO products (vendor_id, category_id, name, slug, description, price, stock_quantity, status)
SELECT 
    vp.id,
    c.id,
    'MacBook Pro M1',
    'macbook-pro-m1',
    'Powerful laptop with M1 chip',
    1299.99,
    30,
    'published'
FROM vendor_profiles vp
CROSS JOIN categories c
WHERE vp.business_name = 'Tech Store'
AND c.slug = 'laptops';

-- Insert sample product images
INSERT INTO product_images (product_id, url, alt_text, is_primary)
SELECT 
    id,
    'https://example.com/images/iphone-13-pro.jpg',
    'iPhone 13 Pro - Space Gray',
    true
FROM products WHERE slug = 'iphone-13-pro';

INSERT INTO product_images (product_id, url, alt_text, is_primary)
SELECT 
    id,
    'https://example.com/images/macbook-pro-m1.jpg',
    'MacBook Pro M1 - Space Gray',
    true
FROM products WHERE slug = 'macbook-pro-m1'; 