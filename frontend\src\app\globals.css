@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors 
           focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 
           disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 
           focus-visible:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 
           focus-visible:ring-secondary-500;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-secondary-200 bg-white px-3 py-2 text-sm 
           ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium 
           placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 
           focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed 
           disabled:opacity-50;
  }

  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed 
           peer-disabled:opacity-70;
  }
}
