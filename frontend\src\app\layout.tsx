// frontend/src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { ClientLayout } from '@/components/layout/client-layout'
import { Toaster } from '@/components/ui/toaster'
import { Providers } from '@/components/providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Market O\'Clock | B2B2C Marketplace',
    template: '%s | Market O\'Clock'
  },
  description: 'Connect, Market, Grow Your Business. Market O\'Clock is a B2B2C marketplace that connects suppliers with retailers and businesses through innovative social engagement features.',
  keywords: ['marketplace', 'B2B2C', 'suppliers', 'retailers', 'social commerce', 'Kenya', 'Nairobi'],
  authors: [{ name: 'Market O\'Clock Team' }],
  creator: 'Market O\'Clock',
  publisher: 'Market O\'Clock',
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Market O\'Clock | B2B2C Marketplace',
    description: 'Connect, Market, Grow Your Business',
    siteName: 'Market O\'Clock',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Market O\'Clock - B2B2C Marketplace',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Market O\'Clock | B2B2C Marketplace',
    description: 'Connect, Market, Grow Your Business',
    images: ['/images/og-image.jpg'],
    creator: '@marketoclock',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-site-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <ClientLayout>
            <div className="relative flex min-h-screen flex-col bg-background">
              <Header />
              <main className="flex-1">
                {children}
              </main>
              <Footer />
            </div>
            <Toaster />
          </ClientLayout>
        </Providers>
      </body>
    </html>
  )
}