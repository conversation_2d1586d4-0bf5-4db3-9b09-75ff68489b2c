'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { SafeProductImage, SafeAvatar } from '@/components/ui/safe-image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Loader2, ArrowLeft, ShoppingCart, Star, Heart, Share2, MessageCircle, Shield, Truck, RotateCcw } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { fetchProductById, Product } from '@/services/productService';
import { useAuthStore } from '@/store/authStore';

export default function ProductDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { user } = useAuthStore();

  useEffect(() => {
    const loadProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        const productData = await fetchProductById(id);
        setProduct(productData);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to load product. Please try again later.');
        toast.error('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadProduct();
    }
  }, [id]);

  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    setQuantity(Math.max(1, Math.min(value, product?.stock || 1)));
  };

  const handleAddToCart = () => {
    if (!product) return;
    if (!user) {
      toast.error('Please log in to add items to your cart');
      return;
    }
    toast.success(`${quantity} x ${product.title} added to your cart!`);
  };

  const handleWishlist = () => {
    if (!user) {
      toast.error('Please log in to add items to your wishlist');
      return;
    }
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.title,
        text: product?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Product link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-lg mb-4">Product not found</p>
        <Link href="/marketplace">
          <Button>Back to Marketplace</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
        <Link href="/marketplace" className="hover:text-gray-900">Marketplace</Link>
        <span>/</span>
        <Link href={`/marketplace?category=${product?.category?.id}`} className="hover:text-gray-900">
          {product?.category?.name}
        </Link>
        <span>/</span>
        <span className="text-gray-900">{product?.title}</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
            <SafeProductImage
              src={product?.images?.[selectedImageIndex]?.url}
              productName={product.title}
              fill
              className="object-cover"
              priority
            />
          </div>

          {/* Thumbnail Images */}
          {product?.images && product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={image.id}
                  type="button"
                  onClick={() => setSelectedImageIndex(index)}
                  title={`View image ${index + 1}`}
                  className={`aspect-square bg-gray-100 rounded-lg overflow-hidden relative border-2 transition-colors ${
                    selectedImageIndex === index ? 'border-blue-500' : 'border-transparent hover:border-gray-300'
                  }`}
                >
                  <SafeProductImage
                    src={image.url}
                    productName={`${product.title} - Image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h1 className="text-3xl font-bold mb-2">{product?.title}</h1>
                <div className="flex items-center gap-4 mb-4">
                  {product?.rating && (
                    <div className="flex items-center">
                      <div className="flex items-center mr-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(product.rating || 0)
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">
                        {product.rating.toFixed(1)} ({product.ratingCount} reviews)
                      </span>
                    </div>
                  )}
                  <Badge variant="outline">{product?.category?.name}</Badge>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleWishlist}
                  className={isWishlisted ? 'text-red-500 border-red-500' : ''}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Price */}
            <div className="mb-6">
              <p className="text-3xl font-bold text-green-600 mb-2">
                ${product?.price.toFixed(2)}
              </p>
              <p className={`text-lg font-semibold ${product?.stock && product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {product?.stock && product.stock > 0
                  ? `In Stock (${product.stock} available)`
                  : 'Out of Stock'
                }
              </p>
            </div>
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Description</h3>
            <p className="text-gray-700 leading-relaxed">{product?.description}</p>
          </div>

          {/* Seller Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Seller Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-semibold">
                    {product?.seller?.businessName || product?.seller?.username}
                  </p>
                  <p className="text-sm text-gray-500">
                    Listed on: {product?.createdAt ? new Date(product.createdAt).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Contact Seller
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Section */}
          {product?.stock && product.stock > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Purchase Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <label htmlFor="quantity" className="font-medium">
                    Quantity:
                  </label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                    >
                      -
                    </Button>
                    <input
                      type="number"
                      id="quantity"
                      min="1"
                      max={product.stock}
                      value={quantity}
                      onChange={handleQuantityChange}
                      className="w-20 border rounded p-2 text-center"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuantity(Math.min(product?.stock || 1, quantity + 1))}
                      disabled={quantity >= (product?.stock || 1)}
                    >
                      +
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between text-lg font-semibold">
                  <span>Total:</span>
                  <span className="text-green-600">
                    ${((product?.price || 0) * quantity).toFixed(2)}
                  </span>
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={handleAddToCart}
                    className="w-full"
                    size="lg"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full"
                    size="lg"
                  >
                    Buy Now
                  </Button>
                </div>

                {!user && (
                  <p className="text-sm text-amber-600 text-center bg-amber-50 p-3 rounded-lg">
                    Please <Link href="/login" className="underline font-medium">log in</Link> to purchase items
                  </p>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-red-600 font-semibold mb-4">This item is currently out of stock</p>
                <Button variant="outline" className="w-full">
                  Notify When Available
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6 text-center">
            <Truck className="h-8 w-8 mx-auto mb-3 text-blue-600" />
            <h4 className="font-semibold mb-2">Free Shipping</h4>
            <p className="text-sm text-gray-600">On orders over $50</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <RotateCcw className="h-8 w-8 mx-auto mb-3 text-green-600" />
            <h4 className="font-semibold mb-2">Easy Returns</h4>
            <p className="text-sm text-gray-600">30-day return policy</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Shield className="h-8 w-8 mx-auto mb-3 text-purple-600" />
            <h4 className="font-semibold mb-2">Secure Payment</h4>
            <p className="text-sm text-gray-600">Your payment is protected</p>
          </CardContent>
        </Card>
      </div>

      {/* Back to Marketplace */}
      <div className="text-center">
        <Link href="/marketplace">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
        </Link>
      </div>
        </div>
      </div>
    </div>
  );
}