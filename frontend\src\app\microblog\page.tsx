// app/microblog/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { MicroblogService, MicroblogPost } from '@/services/microblog-service';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

export default function MicroblogPage() {
  const [posts, setPosts] = useState<MicroblogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await MicroblogService.getPosts();
        setPosts(response.items);
      } catch (err) {
        console.error('Error fetching microblog posts:', err);
        setError('Failed to load posts. Please try again later.');
        toast.error('Failed to load posts');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-center">Market Talk</h1>
      {posts.length === 0 ? (
        <div className="text-center py-16">
          <p className="text-gray-500 text-lg mb-4">No posts yet</p>
          <p className="text-gray-400">Be the first to share something!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {posts.map((post) => (
            <div key={post.id} className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <span className="font-bold text-lg">{post.author.name}</span>
                <span className="text-sm text-gray-500">{post.likes} Likes</span>
              </div>
              <p className="mb-4">{post.content}</p>
              <div className="flex gap-4">
                <Button variant="outline" size="sm">Like</Button>
                <Button variant="outline" size="sm">Comment ({post.comments})</Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
