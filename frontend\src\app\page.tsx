// frontend/src/app/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/store/authStore';
import Image from 'next/image';
import { ArrowRight, Star, Truck, Shield, RefreshCw, Search } from 'lucide-react';
import { HeroSection } from '@/components/home/<USER>';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

interface Post {
  id: string;
  content: string;
}

export default function HomePage() {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const router = useRouter();
  const [posts, setPosts] = useState<Post[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    checkAuth();
    axios
      .get<Post[]>(`${API_URL}/posts?limit=3`)
      .then((res) => setPosts(res.data))
      .catch((err) => console.error('Error fetching posts:', err));
  }, [checkAuth]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Redirect to marketplace with search query
      router.push(`/marketplace?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const featuredProducts = [
    {
      id: 1,
      name: 'Premium Headphones',
      price: 299.99,
      image: '/images/products/headphones.jpg',
      rating: 4.5,
      reviews: 128,
    },
    {
      id: 2,
      name: 'Smart Watch',
      price: 199.99,
      image: '/images/products/smartwatch.jpg',
      rating: 4.3,
      reviews: 89,
    },
    {
      id: 3,
      name: 'Wireless Earbuds',
      price: 149.99,
      image: '/images/products/earbuds.jpg',
      rating: 4.7,
      reviews: 256,
    },
    {
      id: 4,
      name: 'Bluetooth Speaker',
      price: 129.99,
      image: '/images/products/speaker.jpg',
      rating: 4.4,
      reviews: 167,
    },
  ];

  const categories = [
    {
      id: 1,
      name: 'Electronics',
      image: '/images/categories/electronics.jpg',
      count: 1250,
    },
    {
      id: 2,
      name: 'Fashion',
      image: '/images/categories/fashion.jpg',
      count: 850,
    },
    {
      id: 3,
      name: 'Home & Living',
      image: '/images/categories/home.jpg',
      count: 620,
    },
    {
      id: 4,
      name: 'Beauty',
      image: '/images/categories/beauty.jpg',
      count: 430,
    },
  ];

  const features = [
    {
      icon: <Truck className="h-6 w-6" />,
      title: 'Fast Delivery',
      description: 'Free shipping on orders over $50',
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: 'Secure Payment',
      description: '100% secure payment',
    },
    {
      icon: <RefreshCw className="h-6 w-6" />,
      title: 'Easy Returns',
      description: '30 days return policy',
    },
  ];

  return (
    <main>
      <HeroSection />

      {/* Features Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0 p-3 bg-indigo-100 rounded-lg text-indigo-600">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                  <p className="mt-1 text-gray-500">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Shop by Category</h2>
            <p className="mt-4 text-lg text-gray-500">
              Browse through our wide range of categories
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/categories/${category.name.toLowerCase()}`}
                className="group relative overflow-hidden rounded-lg"
              >
                <div className="aspect-w-1 aspect-h-1">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent">
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-xl font-semibold text-white">{category.name}</h3>
                    <p className="text-sm text-gray-200">{category.count} products</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">Featured Products</h2>
              <p className="mt-4 text-lg text-gray-500">
                Discover our most popular products
              </p>
            </div>
            <Link
              href="/products"
              className="inline-flex items-center text-indigo-600 hover:text-indigo-700"
            >
              View all products
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredProducts.map((product) => (
              <div key={product.id} className="group">
                <div className="relative aspect-w-1 aspect-h-1 rounded-lg overflow-hidden">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                  <div className="mt-1 flex items-center">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(product.rating)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }`}
                          fill={i < Math.floor(product.rating) ? 'currentColor' : 'none'}
                        />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">
                      ({product.reviews} reviews)
                    </span>
                  </div>
                  <p className="mt-2 text-lg font-semibold text-gray-900">
                    ${product.price.toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 bg-indigo-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Start Selling?
          </h2>
          <p className="text-xl text-indigo-100 mb-8">
            Join our marketplace and reach thousands of potential customers
          </p>
          <Link
            href="/register"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50"
          >
            Become a Seller
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>
    </main>
  );
}