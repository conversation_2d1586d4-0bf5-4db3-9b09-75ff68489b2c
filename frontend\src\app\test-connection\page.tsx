'use client';

import { useEffect, useState } from 'react';
import api from '@/utils/api';

export default function TestConnection() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const testConnection = async () => {
      try {
        const response = await api.get('/health');
        setStatus('success');
        setMessage('Successfully connected to backend!');
      } catch (error) {
        setStatus('error');
        setMessage('Failed to connect to backend. Please check the console for details.');
        console.error('Connection test failed:', error);
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Testing Backend Connection</h1>
        <div className={`p-4 rounded-lg ${
          status === 'loading' ? 'bg-yellow-100' :
          status === 'success' ? 'bg-green-100' :
          'bg-red-100'
        }`}>
          <p className={`${
            status === 'loading' ? 'text-yellow-800' :
            status === 'success' ? 'text-green-800' :
            'text-red-800'
          }`}>
            {status === 'loading' ? 'Testing connection...' : message}
          </p>
        </div>
      </div>
    </div>
  );
} 