'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert<PERSON>riangle, RefreshCw, Wifi, WifiOff } from 'lucide-react';

interface ApiErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  isNetworkError?: boolean;
}

interface ApiErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onRetry?: () => void;
}

export class ApiErrorBoundary extends React.Component<
  ApiErrorBoundaryProps,
  ApiErrorBoundaryState
> {
  constructor(props: ApiErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ApiErrorBoundaryState {
    const isNetworkError = error.message.includes('fetch') || 
                          error.message.includes('network') ||
                          error.message.includes('Failed to fetch');
    
    return { hasError: true, error, isNetworkError };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('API Error caught by boundary:', error, errorInfo);
    
    // Log API errors for monitoring
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error tracking service
      console.error('API error logged:', { error, errorInfo });
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, isNetworkError: false });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;
      }

      const { isNetworkError } = this.state;

      return (
        <div className="flex items-center justify-center p-4 min-h-[200px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {isNetworkError ? (
                  <WifiOff className="h-12 w-12 text-destructive" />
                ) : (
                  <AlertTriangle className="h-12 w-12 text-destructive" />
                )}
              </div>
              <CardTitle className="text-lg">
                {isNetworkError ? 'Connection Error' : 'Something went wrong'}
              </CardTitle>
              <CardDescription>
                {isNetworkError 
                  ? 'Unable to connect to our servers'
                  : 'There was an error loading this content'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                {isNetworkError ? (
                  <div className="space-y-2">
                    <p>Please check your internet connection and try again.</p>
                    <div className="flex items-center gap-2 text-xs">
                      <Wifi className="h-4 w-4" />
                      <span>Make sure you're connected to the internet</span>
                    </div>
                  </div>
                ) : (
                  <p>
                    We're experiencing some technical difficulties. 
                    Please try again in a moment.
                  </p>
                )}
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                    Show error details
                  </summary>
                  <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                    <p>{this.state.error.message}</p>
                  </div>
                </details>
              )}

              <Button onClick={this.resetError} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
