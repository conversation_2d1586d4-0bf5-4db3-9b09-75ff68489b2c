'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ArrowRight, Store, MessageSquare, CreditCard } from 'lucide-react';

export function HeroSection() {
  return (
    <div className="relative">
      {/* Hero Section */}
      <section className="relative bg-gray-900 text-white">
        <div className="absolute inset-0">
          <Image
            src="/images/hero-bg.jpg"
            alt="Hero background"
            fill
            className="object-cover opacity-50"
            priority
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Connect, Market, Grow Your Business
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Market O&apos;Clock is a B2B2C marketplace that connects suppliers with retailers and businesses through innovative social engagement features.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/register">
                <Button size="lg" className="w-full sm:w-auto">
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/about">
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Key Features
            </h2>
            <p className="text-xl text-gray-600">
              Discover what makes Market O&apos;Clock the ultimate B2B2C marketplace platform.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Marketplace Feature */}
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
              <div className="p-3 bg-indigo-100 rounded-full mb-4">
                <Store className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Marketplace</h3>
              <p className="text-gray-600">
                Connect suppliers with retailers through our efficient marketplace platform.
              </p>
            </div>

            {/* Microblogs Feature */}
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
              <div className="p-3 bg-indigo-100 rounded-full mb-4">
                <MessageSquare className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Microblogs & Blogs</h3>
              <p className="text-gray-600">
                Market products through engaging content with social features.
              </p>
            </div>

            {/* Payment Feature */}
            <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
              <div className="p-3 bg-indigo-100 rounded-full mb-4">
                <CreditCard className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Multi-Payment Support</h3>
              <p className="text-gray-600">
                Secure and flexible payment options for all transactions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-indigo-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to grow your business?
          </h2>
          <p className="text-xl text-indigo-100 mb-8">
            Join Market O&apos;Clock today and connect with suppliers and retailers from various industries.
          </p>
          <Link href="/register">
            <Button size="lg" variant="secondary" className="w-full sm:w-auto">
              Sign Up Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
} 