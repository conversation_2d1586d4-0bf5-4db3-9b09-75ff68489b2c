// frontend/src/components/layout/MobileNav.tsx
"use client";

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  User, 
  ShoppingCart, 
  Heart, 
  Bell, 
  Settings, 
  Package, 
  LogOut,
  Home,
  Store,
  Grid3x3,
  BookOpen,
  Info,
  Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/hooks/useAuth';

interface NavLink {
  href: string;
  label: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface MobileNavProps {
  navLinks: NavLink[];
  isAuthenticated: boolean;
  user: User | null;
  onClose: () => void;
}

const iconMap = {
  'Home': Home,
  'Marketplace': Store,
  'Categories': Grid3x3,
  'Blog': BookOpen,
  'About': Info,
  'Contact': Mail,
};

export const MobileNav: React.FC<MobileNavProps> = ({
  navLinks,
  isAuthenticated,
  user,
  onClose
}) => {
  const router = useRouter();
  const { logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
      onClose();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleNavigation = (href: string) => {
    router.push(href);
    onClose();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center space-x-2 p-4 border-b">
        <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
          <span className="text-primary-foreground font-bold text-lg">M</span>
        </div>
        <span className="font-bold text-xl">Market O'Clock</span>
      </div>

      {/* User section */}
      {isAuthenticated && user ? (
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3 mb-3">
            <div className="h-10 w-10 bg-primary rounded-full flex items-center justify-center">
              {user.avatar ? (
                <img 
                  src={user.avatar} 
                  alt={user.name}
                  className="h-10 w-10 rounded-full object-cover"
                />
              ) : (
                <User className="h-5 w-5 text-primary-foreground" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{user.name}</p>
              <p className="text-xs text-muted-foreground truncate">{user.email}</p>
            </div>
          </div>
          
          {/* Quick actions for authenticated users */}
          <div className="grid grid-cols-3 gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="flex flex-col h-auto py-2"
              onClick={() => handleNavigation('/cart')}
            >
              <ShoppingCart className="h-4 w-4 mb-1" />
              <span className="text-xs">Cart</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex flex-col h-auto py-2"
              onClick={() => handleNavigation('/dashboard/wishlist')}
            >
              <Heart className="h-4 w-4 mb-1" />
              <span className="text-xs">Wishlist</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="flex flex-col h-auto py-2"
              onClick={() => handleNavigation('/dashboard/notifications')}
            >
              <Bell className="h-4 w-4 mb-1" />
              <span className="text-xs">Alerts</span>
            </Button>
          </div>
        </div>
      ) : (
        /* Auth buttons for non-authenticated users */
        <div className="p-4 border-b">
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => handleNavigation('/login')}
            >
              Sign in
            </Button>
            <Button 
              size="sm" 
              className="flex-1"
              onClick={() => handleNavigation('/register')}
            >
              Sign up
            </Button>
          </div>
        </div>
      )}

      {/* Main navigation */}
      <div className="flex-1 overflow-y-auto">
        <nav className="p-2">
          <div className="space-y-1">
            {navLinks.map((link) => {
              const IconComponent = iconMap[link.label as keyof typeof iconMap];
              return (
                <Button
                  key={link.href}
                  variant="ghost"
                  className="w-full justify-start h-auto py-3"
                  onClick={() => handleNavigation(link.href)}
                >
                  {IconComponent && (
                    <IconComponent className="h-4 w-4 mr-3" />
                  )}
                  <span className="text-sm font-medium">{link.label}</span>
                </Button>
              );
            })}
          </div>

          {/* Authenticated user menu items */}
          {isAuthenticated && (
            <>
              <Separator className="my-4" />
              <div className="space-y-1">
                <p className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Account
                </p>
                
                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto py-3"
                  onClick={() => handleNavigation('/dashboard')}
                >
                  <User className="h-4 w-4 mr-3" />
                  <span className="text-sm font-medium">Dashboard</span>
                </Button>

                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto py-3"
                  onClick={() => handleNavigation('/dashboard/orders')}
                >
                  <Package className="h-4 w-4 mr-3" />
                  <span className="text-sm font-medium">My Orders</span>
                </Button>

                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto py-3"
                  onClick={() => handleNavigation('/dashboard/settings')}
                >
                  <Settings className="h-4 w-4 mr-3" />
                  <span className="text-sm font-medium">Settings</span>
                </Button>
              </div>
            </>
          )}

          {/* Additional links */}
          <Separator className="my-4" />
          <div className="space-y-1">
            <p className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Support
            </p>
            
            <Button
              variant="ghost"
              className="w-full justify-start h-auto py-3"
              onClick={() => handleNavigation('/faq')}
            >
              <span className="text-sm font-medium">FAQ</span>
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start h-auto py-3"
              onClick={() => handleNavigation('/terms')}
            >
              <span className="text-sm font-medium">Terms of Service</span>
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start h-auto py-3"
              onClick={() => handleNavigation('/privacy')}
            >
              <span className="text-sm font-medium">Privacy Policy</span>
            </Button>
          </div>
        </nav>
      </div>

      {/* Footer with logout */}
      {isAuthenticated && (
        <div className="p-4 border-t">
          <Button
            variant="ghost"
            className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4 mr-3" />
            <span className="text-sm font-medium">Sign out</span>
          </Button>
        </div>
      )}
    </div>
  );
};