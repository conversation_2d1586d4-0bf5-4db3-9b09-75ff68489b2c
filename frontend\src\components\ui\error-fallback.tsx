'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ErrorFallbackProps {
  error?: Error | null;
  resetError?: () => void;
  variant?: 'page' | 'component' | 'inline';
  title?: string;
  description?: string;
  showDetails?: boolean;
  className?: string;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  variant = 'component',
  title,
  description,
  showDetails = false,
  className = ''
}) => {
  const handleReload = () => {
    if (resetError) {
      resetError();
    } else {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoBack = () => {
    window.history.back();
  };

  // Default error messages based on variant
  const getDefaultTitle = () => {
    switch (variant) {
      case 'page':
        return 'Oops! Something went wrong';
      case 'component':
        return 'Unable to load content';
      case 'inline':
        return 'Error';
      default:
        return 'Something went wrong';
    }
  };

  const getDefaultDescription = () => {
    switch (variant) {
      case 'page':
        return 'We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.';
      case 'component':
        return 'This section could not be loaded. Please try again.';
      case 'inline':
        return 'Failed to load';
      default:
        return 'An unexpected error occurred.';
    }
  };

  const errorTitle = title || getDefaultTitle();
  const errorDescription = description || getDefaultDescription();

  // Inline variant for small error states
  if (variant === 'inline') {
    return (
      <div className={`flex items-center justify-center p-2 text-center ${className}`}>
        <div className="flex flex-col items-center space-y-2">
          <AlertTriangle className="h-5 w-5 text-destructive" />
          <div>
            <p className="text-sm font-medium text-destructive">{errorTitle}</p>
            {errorDescription && (
              <p className="text-xs text-muted-foreground">{errorDescription}</p>
            )}
          </div>
          {resetError && (
            <Button size="sm" variant="outline" onClick={handleReload}>
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Component variant for section errors
  if (variant === 'component') {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <CardTitle className="text-lg font-semibold text-center mb-2">
            {errorTitle}
          </CardTitle>
          <CardDescription className="text-center mb-4 max-w-md">
            {errorDescription}
          </CardDescription>
          
          {showDetails && error && (
            <details className="w-full max-w-md mb-4">
              <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                Show error details
              </summary>
              <div className="mt-2 p-3 bg-muted rounded-md">
                <code className="text-xs text-wrap break-all">
                  {error.message}
                </code>
                {error.stack && (
                  <pre className="mt-2 text-xs text-muted-foreground overflow-auto max-h-32">
                    {error.stack}
                  </pre>
                )}
              </div>
            </details>
          )}

          <div className="flex space-x-2">
            {resetError && (
              <Button onClick={handleReload} variant="default">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            <Button onClick={handleGoBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Page variant for full page errors
  return (
    <div className={`min-h-screen flex items-center justify-center p-4 ${className}`}>
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <AlertTriangle className="h-20 w-20 text-destructive mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-foreground mb-2">
            {errorTitle}
          </h1>
          <p className="text-muted-foreground text-lg">
            {errorDescription}
          </p>
        </div>

        {showDetails && error && (
          <details className="mb-8 text-left">
            <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground mb-2">
              Show technical details
            </summary>
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold text-sm mb-2">Error Message:</h3>
              <code className="text-xs bg-background p-2 rounded block mb-3 break-all">
                {error.message}
              </code>
              {error.stack && (
                <>
                  <h3 className="font-semibold text-sm mb-2">Stack Trace:</h3>
                  <pre className="text-xs bg-background p-2 rounded overflow-auto max-h-32 text-muted-foreground">
                    {error.stack}
                  </pre>
                </>
              )}
            </div>
          </details>
        )}

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 justify-center">
          {resetError && (
            <Button onClick={handleReload} className="w-full sm:w-auto">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          <Button onClick={handleGoHome} variant="outline" className="w-full sm:w-auto">
            <Home className="h-4 w-4 mr-2" />
            Go Home
          </Button>
          <Button onClick={handleGoBack} variant="ghost" className="w-full sm:w-auto">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>

        <div className="mt-8 text-sm text-muted-foreground">
          <p>If this problem persists, please contact our support team.</p>
          <p className="mt-1">
            Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

// Specific error components for common scenarios
export const NotFoundError: React.FC<{ resource?: string }> = ({ resource = 'page' }) => (
  <ErrorFallback
    variant="page"
    title={`${resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found`}
    description={`The ${resource} you're looking for doesn't exist or may have been moved.`}
    resetError={() => window.history.back()}
  />
);

export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <ErrorFallback
    variant="component"
    title="Connection Error"
    description="Unable to connect to our servers. Please check your internet connection and try again."
    resetError={onRetry}
  />
);

export const UnauthorizedError: React.FC = () => (
  <ErrorFallback
    variant="page"
    title="Access Denied"
    description="You don't have permission to access this resource. Please sign in or contact support."
    resetError={() => window.location.href = '/login'}
  />
);

export const ServerError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <ErrorFallback
    variant="component"
    title="Server Error"
    description="Our servers are experiencing issues. Please try again in a few moments."
    resetError={onRetry}
  />
);

// Error boundary wrapper component
export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<any> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ComponentType<any> }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || ErrorFallback;
      return (
        <FallbackComponent
          error={this.state.error}
          resetError={() => this.setState({ hasError: false, error: null })}
        />
      );
    }

    return this.props.children;
  }
} 