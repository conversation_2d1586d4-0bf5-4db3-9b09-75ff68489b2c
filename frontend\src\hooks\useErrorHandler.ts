'use client';

import { useCallback, useState } from 'react';
import { toast } from '@/hooks/use-toast';

interface ErrorState {
  error: Error | null;
  isError: boolean;
  errorMessage: string | null;
}

interface UseErrorHandlerReturn extends ErrorState {
  handleError: (error: Error | string) => void;
  clearError: () => void;
  retryWithErrorHandling: <T>(fn: () => Promise<T>) => Promise<T | null>;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    errorMessage: null,
  });

  const handleError = useCallback((error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    const message = errorObj.message || 'An unexpected error occurred';

    setErrorState({
      error: errorObj,
      isError: true,
      errorMessage: message,
    });

    // Show toast notification
    toast({
      title: 'Error',
      description: message,
      variant: 'destructive',
    });

    // Log error for debugging
    console.error('Error handled:', errorObj);

    // In production, send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error tracking service (e.g., Sentry)
      console.error('Error logged to tracking service:', errorObj);
    }
  }, []);

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      errorMessage: null,
    });
  }, []);

  const retryWithErrorHandling = useCallback(async <T>(
    fn: () => Promise<T>
  ): Promise<T | null> => {
    try {
      clearError();
      const result = await fn();
      return result;
    } catch (error) {
      handleError(error as Error);
      return null;
    }
  }, [handleError, clearError]);

  return {
    ...errorState,
    handleError,
    clearError,
    retryWithErrorHandling,
  };
}

// Specific error handlers for common scenarios
export function useApiErrorHandler() {
  const { handleError, ...rest } = useErrorHandler();

  const handleApiError = useCallback((error: any) => {
    let message = 'An error occurred while communicating with the server';

    if (error?.response) {
      // Server responded with error status
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          message = data?.message || 'Invalid request';
          break;
        case 401:
          message = 'You are not authorized. Please log in again.';
          break;
        case 403:
          message = 'You do not have permission to perform this action';
          break;
        case 404:
          message = 'The requested resource was not found';
          break;
        case 422:
          message = data?.message || 'Validation error';
          break;
        case 429:
          message = 'Too many requests. Please try again later.';
          break;
        case 500:
          message = 'Internal server error. Please try again later.';
          break;
        default:
          message = data?.message || `Server error (${status})`;
      }
    } else if (error?.request) {
      // Network error
      message = 'Network error. Please check your connection and try again.';
    } else if (error?.message) {
      message = error.message;
    }

    handleError(new Error(message));
  }, [handleError]);

  return {
    ...rest,
    handleError: handleApiError,
  };
}

export function useFormErrorHandler() {
  const { handleError, ...rest } = useErrorHandler();

  const handleFormError = useCallback((error: any, fieldErrors?: Record<string, string>) => {
    if (fieldErrors) {
      // Handle field-specific errors
      const firstError = Object.values(fieldErrors)[0];
      handleError(new Error(firstError || 'Form validation failed'));
    } else {
      handleError(error);
    }
  }, [handleError]);

  return {
    ...rest,
    handleError: handleFormError,
  };
}
