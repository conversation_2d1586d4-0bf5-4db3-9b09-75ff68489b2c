import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token,
    },
  }
);

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/api/dashboard/:path*',
    '/api/posts/:path*',
    '/api/comments/:path*',
    '/api/likes/:path*',
    '/api/follows/:path*',
    '/api/notifications/:path*',
    '/api/messages/:path*',
    '/api/orders/:path*',
    '/api/cart/:path*',
    '/api/wishlist/:path*',
    '/api/reviews/:path*',
    '/api/products/:path*',
    '/api/categories/:path*',
    '/api/tags/:path*',
    '/api/users/:path*',
    '/api/settings/:path*',
    '/api/profile/:path*',
    '/api/addresses/:path*',
    '/api/payments/:path*',
    '/api/shipping/:path*',
    '/api/taxes/:path*',
    '/api/discounts/:path*',
    '/api/coupons/:path*',
    '/api/refunds/:path*',
    '/api/returns/:path*',
    '/api/claims/:path*',
    '/api/disputes/:path*',
    '/api/feedback/:path*',
    '/api/reports/:path*',
    '/api/analytics/:path*',
    '/api/logs/:path*',
    '/api/audit/:path*',
    '/api/security/:path*',
    '/api/backup/:path*',
    '/api/restore/:path*',
    '/api/import/:path*',
    '/api/export/:path*',
    '/api/sync/:path*',
    '/api/webhooks/:path*',
    '/api/integrations/:path*',
    '/api/plugins/:path*',
    '/api/themes/:path*',
    '/api/widgets/:path*',
    '/api/blocks/:path*',
    '/api/pages/:path*',
    '/api/posts/:path*',
    '/api/comments/:path*',
    '/api/likes/:path*',
    '/api/follows/:path*',
    '/api/notifications/:path*',
    '/api/messages/:path*',
    '/api/orders/:path*',
    '/api/cart/:path*',
    '/api/wishlist/:path*',
    '/api/reviews/:path*',
    '/api/products/:path*',
    '/api/categories/:path*',
    '/api/tags/:path*',
    '/api/users/:path*',
    '/api/settings/:path*',
    '/api/profile/:path*',
    '/api/addresses/:path*',
    '/api/payments/:path*',
    '/api/shipping/:path*',
    '/api/taxes/:path*',
    '/api/discounts/:path*',
    '/api/coupons/:path*',
    '/api/refunds/:path*',
    '/api/returns/:path*',
    '/api/claims/:path*',
    '/api/disputes/:path*',
    '/api/feedback/:path*',
    '/api/reports/:path*',
    '/api/analytics/:path*',
    '/api/logs/:path*',
    '/api/audit/:path*',
    '/api/security/:path*',
    '/api/backup/:path*',
    '/api/restore/:path*',
    '/api/import/:path*',
    '/api/export/:path*',
    '/api/sync/:path*',
    '/api/webhooks/:path*',
    '/api/integrations/:path*',
    '/api/plugins/:path*',
    '/api/themes/:path*',
    '/api/widgets/:path*',
    '/api/blocks/:path*',
    '/api/pages/:path*',
  ],
}; 