import { api } from '@/utils/api';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  role: string;
  fullName?: string;
  businessName?: string;
}

export interface AuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
    username: string;
    role: string;
    fullName?: string;
    businessName?: string;
  };
}

export interface User {
  id: string;
  email: string;
  username: string;
  role: string;
  fullName?: string;
  businessName?: string;
}

class AuthService {
  private readonly baseUrl = '/api/auth';

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>(`${this.baseUrl}/login`, credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
    }
    return response.data;
  }

  async register(data: RegisterData): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>(`${this.baseUrl}/register`, data);
    return response.data;
  }

  async logout(): Promise<void> {
    localStorage.removeItem('token');
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await api.get<User>(`${this.baseUrl}/me`);
      return response.data;
    } catch (error) {
      return null;
    }
  }

  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>(`${this.baseUrl}/forgot-password`, { email });
    return response.data;
  }

  async resetPassword(token: string, password: string): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>(`${this.baseUrl}/reset-password`, {
      token,
      password,
    });
    return response.data;
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>(`${this.baseUrl}/verify-email`, { token });
    return response.data;
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
}

export const authService = new AuthService();