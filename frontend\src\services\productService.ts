import { api } from '@/utils/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  stock: number;
  isActive: boolean;
  seller: {
    id: string;
    username: string;
    fullName?: string;
    businessName?: string;
  };
  category: {
    id: string;
    name: string;
  };
  images: {
    id: string;
    url: string;
  }[];
  createdAt: string;
  updatedAt: string;
  rating?: number;
  ratingCount: number;
  viewCount: number;
  purchaseCount: number;
}

export interface ProductsResponse {
  products: Product[];
  totalPages: number;
}

export interface FilterProductsParams {
  search?: string;
  sort?: 'popular' | 'newest' | 'price-low' | 'price-high';
  page?: number;
  limit?: number;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  userId?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
}

export const fetchProducts = async (params?: FilterProductsParams): Promise<ProductsResponse> => {
  const queryParams = new URLSearchParams();

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
  }

  const response = await api.get(`/api/products?${queryParams.toString()}`);
  return response.data;
};

export const fetchProductById = async (id: string): Promise<Product> => {
  const response = await api.get(`/api/products/${id}`);
  return response.data;
};

export const fetchCategories = async (): Promise<Category[]> => {
  const response = await api.get('/api/categories');
  return response.data;
};

export const createProduct = async (product: {
  title: string;
  description: string;
  price: number;
  stock: number;
  categoryId: string;
  images: string[];
}): Promise<Product> => {
  const response = await api.post('/api/products', product);
  return response.data;
};

// Order-related interfaces and functions
export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

export interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    title: string;
    images: { id: string; url: string }[];
  };
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod?: string;
  shippingAddress?: string;
  contactPhone?: string;
  notes?: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export const fetchUserOrders = async (): Promise<Order[]> => {
  const response = await api.get('/api/orders/my-orders');
  return response.data;
};

export const fetchOrderById = async (id: string): Promise<Order> => {
  const response = await api.get(`/api/orders/${id}`);
  return response.data;
};

export const cancelOrder = async (id: string): Promise<Order> => {
  const response = await api.patch(`/api/orders/${id}/cancel`);
  return response.data;
};