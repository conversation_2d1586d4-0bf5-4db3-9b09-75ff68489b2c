{"name": "marketoclock", "version": "1.0.0", "description": "Market O'Clock - A modern multi-service e-commerce and marketplace platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"pnpm --filter marketoclock-backend dev\" \"pnpm --filter marketoclock-frontend dev\"", "dev:backend": "pnpm --filter marketoclock-backend dev", "dev:frontend": "pnpm --filter marketoclock-frontend dev", "build": "pnpm --filter marketoclock-backend build && pnpm --filter marketoclock-frontend build", "build:backend": "pnpm --filter marketoclock-backend build", "build:frontend": "pnpm --filter marketoclock-frontend build", "start": "concurrently \"pnpm --filter marketoclock-backend start\" \"pnpm --filter marketoclock-frontend start\"", "start:backend": "pnpm --filter marketoclock-backend start", "start:frontend": "pnpm --filter marketoclock-frontend start", "test": "pnpm --filter marketoclock-backend test && pnpm --filter marketoclock-frontend test", "test:backend": "pnpm --filter marketoclock-backend test", "test:frontend": "pnpm --filter marketoclock-frontend test", "lint": "pnpm --filter marketoclock-backend lint && pnpm --filter marketoclock-frontend lint", "lint:backend": "pnpm --filter marketoclock-backend lint", "lint:frontend": "pnpm --filter marketoclock-frontend lint", "clean": "pnpm --filter marketoclock-backend clean && pnpm --filter marketoclock-frontend clean", "install:all": "pnpm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "sharp", "unrs-resolver"]}}